# Leads Export API Documentation

## Overview

The Leads Export API endpoint allows users to export all leads data to an Excel file (.xlsx) that is automatically downloaded by the browser. This feature is perfect for generating reports, data backups, or sharing lead information with stakeholders.

## Endpoint

**GET** `/api/v1/leads/export`

## Features

- ✅ **Automatic Download**: File is sent with proper headers for immediate browser download
- ✅ **Comprehensive Data**: Includes all lead relationships and activity counts
- ✅ **Professional Formatting**: Excel file with styled headers, borders, and alternating row colors
- ✅ **Search Filtering**: Optional search parameter to export specific leads
- ✅ **Dynamic Filename**: Includes current date in filename
- ✅ **Export Summary**: Includes summary information at the bottom of the file
- ✅ **Browser Compatible**: Works with all modern browsers and frontend frameworks

## Request Format

### Basic Export (All Leads)
```bash
GET /api/v1/leads/export
```

### Filtered Export (Search)
```bash
GET /api/v1/leads/export?search=corporate
```

## Query Parameters

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `search` | string | No | Search term to filter leads before export | `corporate`, `john`, `+254700000001` |

### Search Functionality

The search parameter filters leads by:
- **Customer Name**: Partial match, case-insensitive
- **Client ID**: Partial match, case-insensitive  
- **Phone Number**: Partial match
- **Lead Type**: Partial match, case-insensitive
- **RM User Name**: Partial match, case-insensitive
- **Branch Name**: Partial match, case-insensitive

## Response Format

### Headers
```
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
Content-Disposition: attachment; filename="leads-export-2025-07-30.xlsx"
Content-Length: [file-size-in-bytes]
```

### Body
Binary Excel file (.xlsx) containing leads data

## Excel File Structure

### Columns Included

| Column | Description | Example |
|--------|-------------|---------|
| Lead Name | Lead/customer name | `John Doe` |
| Phone Number | Contact phone number | `+254700000001` |
| Client ID | Unique client identifier | `CLI001` |
| Lead Type | Type of lead | `New`, `Existing` |
| Lead Status | Current status | `Pending`, `Hot`, `Cold` |
| Parent Lead | Parent lead name (if child) | `ABC Corporation` |
| Branch | Branch name | `Nairobi Branch` |
| Region | Region name | `Central Region` |
| Customer Category | Category name | `Individual`, `Corporate` |
| ISIC Sector | Industry sector | `Technology`, `Manufacturing` |
| Employer | Employer name | `Tech Solutions Ltd` |
| RM User | Relationship manager | `Jane Smith` |
| Contact Persons | Associated contacts | `John Doe (+254700000001); Jane Doe (+254700000002)` |
| No. of Visits | Visit count | `3` |
| No. of Calls | Call count | `7` |
| Last Interaction | Last activity date | `2025-07-30` |
| Created Date | Lead creation date | `2025-07-15` |

### File Features

- **Styled Headers**: Bold white text on blue background
- **Alternating Rows**: Light gray background for better readability
- **Auto-fit Columns**: Columns automatically sized for content
- **Borders**: Clean borders around all cells
- **Export Summary**: Summary section with total count, export date, and search filter

## Frontend Integration

### JavaScript (Vanilla)

```javascript
async function exportLeads(search = '') {
    try {
        const url = search 
            ? `/api/v1/leads/export?search=${encodeURIComponent(search)}`
            : '/api/v1/leads/export';
            
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            }
        });

        if (!response.ok) {
            throw new Error(`Export failed: ${response.statusText}`);
        }

        // Get filename from Content-Disposition header
        const contentDisposition = response.headers.get('Content-Disposition');
        const filename = contentDisposition
            ? contentDisposition.split('filename=')[1].replace(/"/g, '')
            : 'leads-export.xlsx';

        // Create blob and trigger download
        const blob = await response.blob();
        const url2 = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url2;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url2);

        return { success: true, filename };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

// Usage
exportLeads(); // Export all leads
exportLeads('corporate'); // Export filtered leads
```

### React Example

```jsx
import { useState } from 'react';

const ExportButton = ({ search = '', onSuccess, onError }) => {
    const [loading, setLoading] = useState(false);
    
    const handleExport = async () => {
        setLoading(true);
        try {
            const result = await exportLeads(search);
            if (result.success) {
                onSuccess?.(result.filename);
            } else {
                onError?.(result.error);
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <button 
            onClick={handleExport} 
            disabled={loading}
            className="export-button"
        >
            {loading ? '⏳ Exporting...' : '📥 Export Leads'}
        </button>
    );
};

// Usage
<ExportButton 
    search="corporate"
    onSuccess={(filename) => toast.success(`Exported to ${filename}`)}
    onError={(error) => toast.error(`Export failed: ${error}`)}
/>
```

### Vue.js Example

```vue
<template>
    <button 
        @click="exportLeads" 
        :disabled="loading"
        class="export-button"
    >
        {{ loading ? '⏳ Exporting...' : '📥 Export Leads' }}
    </button>
</template>

<script>
export default {
    props: {
        search: { type: String, default: '' }
    },
    data() {
        return {
            loading: false
        };
    },
    methods: {
        async exportLeads() {
            this.loading = true;
            try {
                const result = await exportLeads(this.search);
                if (result.success) {
                    this.$emit('success', result.filename);
                } else {
                    this.$emit('error', result.error);
                }
            } finally {
                this.loading = false;
            }
        }
    }
};
</script>
```

### Angular Example

```typescript
import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
    selector: 'app-export-button',
    template: `
        <button 
            (click)="exportLeads()" 
            [disabled]="loading"
            class="export-button"
        >
            {{ loading ? '⏳ Exporting...' : '📥 Export Leads' }}
        </button>
    `
})
export class ExportButtonComponent {
    @Input() search: string = '';
    @Output() success = new EventEmitter<string>();
    @Output() error = new EventEmitter<string>();
    
    loading = false;

    async exportLeads() {
        this.loading = true;
        try {
            const result = await exportLeads(this.search);
            if (result.success) {
                this.success.emit(result.filename);
            } else {
                this.error.emit(result.error);
            }
        } finally {
            this.loading = false;
        }
    }
}
```

## Usage Examples

### Example 1: Basic Export Button

```html
<button onclick="exportAllLeads()">📥 Export All Leads</button>

<script>
async function exportAllLeads() {
    const result = await exportLeads();
    if (result.success) {
        alert(`Successfully exported to ${result.filename}`);
    } else {
        alert(`Export failed: ${result.error}`);
    }
}
</script>
```

### Example 2: Export with Search

```html
<input type="text" id="searchInput" placeholder="Search leads...">
<button onclick="exportFilteredLeads()">📥 Export Filtered</button>

<script>
async function exportFilteredLeads() {
    const search = document.getElementById('searchInput').value;
    const result = await exportLeads(search);
    if (result.success) {
        alert(`Successfully exported filtered leads to ${result.filename}`);
    } else {
        alert(`Export failed: ${result.error}`);
    }
}
</script>
```

### Example 3: Advanced Export with Progress

```javascript
class LeadsExporter {
    constructor() {
        this.isExporting = false;
    }

    async export(search = '', onProgress = null) {
        if (this.isExporting) return;
        
        this.isExporting = true;
        onProgress?.('Preparing export...');

        try {
            onProgress?.('Fetching data...');
            const result = await exportLeads(search);
            
            if (result.success) {
                onProgress?.(`Successfully exported to ${result.filename}`);
                return result;
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            onProgress?.(`Export failed: ${error.message}`);
            throw error;
        } finally {
            this.isExporting = false;
        }
    }
}

// Usage
const exporter = new LeadsExporter();
await exporter.export('corporate', (status) => {
    console.log(status);
    document.getElementById('status').textContent = status;
});
```

## Error Handling

### Common Error Responses

| Status Code | Description | Solution |
|-------------|-------------|----------|
| 400 | Bad Request - Invalid search parameter | Check search parameter format |
| 500 | Internal Server Error | Check server logs, database connection |
| 503 | Service Unavailable | Server overloaded, try again later |

### Frontend Error Handling

```javascript
async function exportLeadsWithErrorHandling(search = '') {
    try {
        const response = await fetch(`/api/v1/leads/export?search=${encodeURIComponent(search)}`);
        
        if (!response.ok) {
            switch (response.status) {
                case 400:
                    throw new Error('Invalid search parameters');
                case 500:
                    throw new Error('Server error. Please try again later.');
                case 503:
                    throw new Error('Service temporarily unavailable');
                default:
                    throw new Error(`Export failed: ${response.statusText}`);
            }
        }

        // Handle successful response...
        
    } catch (error) {
        if (error.name === 'TypeError') {
            // Network error
            throw new Error('Network error. Please check your connection.');
        }
        throw error;
    }
}
```

## Performance Considerations

### File Size Expectations

| Lead Count | Approximate File Size | Download Time (Broadband) |
|------------|----------------------|---------------------------|
| 100 leads | ~15 KB | < 1 second |
| 1,000 leads | ~150 KB | 1-2 seconds |
| 10,000 leads | ~1.5 MB | 3-5 seconds |
| 50,000 leads | ~7.5 MB | 10-15 seconds |

### Best Practices

1. **Show Loading State**: Always show loading indicator during export
2. **Handle Large Datasets**: Consider pagination for very large exports
3. **Error Boundaries**: Implement proper error handling
4. **User Feedback**: Provide clear success/error messages
5. **File Management**: Clean up blob URLs after download

## Security Considerations

- **Authentication**: Endpoint respects user authentication
- **Authorization**: Users can only export leads they have access to
- **Rate Limiting**: May have rate limits to prevent abuse
- **Data Sensitivity**: Exported files contain sensitive data - handle appropriately

## Browser Compatibility

- ✅ **Chrome 60+**
- ✅ **Firefox 55+**
- ✅ **Safari 12+**
- ✅ **Edge 79+**
- ✅ **Mobile browsers** (iOS Safari, Chrome Mobile)

The export functionality provides a seamless way for users to download comprehensive lead data in a professional Excel format, making it perfect for reporting, analysis, and data sharing workflows.
