# All Leads API Documentation

## Overview

The All Leads API endpoint provides access to the complete dataset of leads without pagination. This is useful for scenarios where you need all leads at once, such as data exports, reports, analytics, or populating UI components.

## Endpoint

**GET** `/api/v1/leads/all`

## Features

- **No Pagination**: Returns all leads in a single response
- **Optional Search**: Filter results with search terms
- **Complete Data**: Includes all relationship data (parent leads, branches, categories, etc.)
- **Performance Optimized**: Single database query with efficient joins
- **Consistent Format**: Same lead structure as paginated endpoint

## Request Format

### Basic Request
```bash
GET /api/v1/leads/all
```

### With Search Filter
```bash
GET /api/v1/leads/all?search=john
```

## Query Parameters

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `search` | string | No | Search term to filter leads | `john`, `+************`, `corporate` |

### Search Functionality

The search parameter filters leads by:
- **Customer Name**: Partial match, case-insensitive
- **Client ID**: Partial match, case-insensitive  
- **Phone Number**: Partial match
- **Lead Type**: Partial match, case-insensitive
- **RM User Name**: Partial match, case-insensitive
- **Branch Name**: Partial match, case-insensitive

## Response Format

```json
{
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "anchor_name": "Parent Lead Name",
      "lead_name": "Customer Name",
      "status": "pending",
      "phoneNumber": "+************",
      "no_of_visits": 2,
      "no_of_calls": 5,
      "last_interaction": "2025-07-30T10:30:00.000Z",
      "customer_category": {
        "id": "cat-id",
        "name": "Individual"
      },
      "isic_sector": {
        "id": "sector-id", 
        "name": "Technology"
      },
      "branch": {
        "id": "branch-id",
        "name": "Nairobi Branch"
      }
    }
  ],
  "total": 1250,
  "message": "Retrieved all 1250 leads successfully"
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `data` | array | Array of lead objects |
| `total` | number | Total number of leads returned |
| `message` | string | Success message with count |

### Lead Object Structure

Each lead in the `data` array contains:

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Unique lead identifier (UUID) |
| `anchor_name` | string\|null | Parent lead name (if child lead) |
| `lead_name` | string | Customer/lead name |
| `status` | string | Lead status |
| `phoneNumber` | string | Customer phone number |
| `no_of_visits` | number | Number of visits recorded |
| `no_of_calls` | number | Number of calls recorded |
| `last_interaction` | string\|null | Last activity timestamp (ISO 8601) |
| `customer_category` | object\|null | Customer category details |
| `isic_sector` | object\|null | ISIC sector details |
| `branch` | object\|null | Branch details |

## Usage Examples

### Example 1: Get All Leads
```bash
curl -X GET "http://localhost:3000/api/v1/leads/all" \
  -H "Accept: application/json"
```

**Response:**
```json
{
  "data": [
    {
      "id": "lead-1",
      "anchor_name": null,
      "lead_name": "John Doe",
      "status": "pending",
      "phoneNumber": "+************",
      "no_of_visits": 0,
      "no_of_calls": 0,
      "last_interaction": null,
      "customer_category": {
        "id": "cat-1",
        "name": "Individual"
      },
      "isic_sector": null,
      "branch": {
        "id": "branch-1", 
        "name": "Nairobi Branch"
      }
    }
  ],
  "total": 1,
  "message": "Retrieved all 1 leads successfully"
}
```

### Example 2: Search Leads
```bash
curl -X GET "http://localhost:3000/api/v1/leads/all?search=corporate" \
  -H "Accept: application/json"
```

**Response:**
```json
{
  "data": [
    {
      "id": "lead-2",
      "anchor_name": null,
      "lead_name": "ABC Corporation",
      "status": "pending",
      "phoneNumber": "+254700000002",
      "no_of_visits": 3,
      "no_of_calls": 7,
      "last_interaction": "2025-07-30T09:15:00.000Z",
      "customer_category": {
        "id": "cat-2",
        "name": "Corporate"
      },
      "isic_sector": {
        "id": "sector-1",
        "name": "Manufacturing"
      },
      "branch": {
        "id": "branch-2",
        "name": "Mombasa Branch"
      }
    }
  ],
  "total": 1,
  "message": "Retrieved all 1 leads successfully matching \"corporate\""
}
```

### Example 3: JavaScript/Frontend Usage
```javascript
// Get all leads
async function getAllLeads() {
  try {
    const response = await fetch('/api/v1/leads/all');
    const data = await response.json();
    
    console.log(`Total leads: ${data.total}`);
    console.log('Leads:', data.data);
    
    return data.data;
  } catch (error) {
    console.error('Error fetching leads:', error);
  }
}

// Search leads
async function searchLeads(searchTerm) {
  try {
    const response = await fetch(`/api/v1/leads/all?search=${encodeURIComponent(searchTerm)}`);
    const data = await response.json();
    
    return data.data;
  } catch (error) {
    console.error('Error searching leads:', error);
  }
}

// Populate dropdown
async function populateLeadsDropdown() {
  const leads = await getAllLeads();
  const select = document.getElementById('leads-select');
  
  leads.forEach(lead => {
    const option = document.createElement('option');
    option.value = lead.id;
    option.textContent = `${lead.lead_name} (${lead.phoneNumber || 'No phone'})`;
    select.appendChild(option);
  });
}
```

## Use Cases

### 📊 **Data Export & Reports**
```javascript
// Export all leads to CSV
const leads = await fetch('/api/v1/leads/all').then(r => r.json());
const csv = convertToCSV(leads.data);
downloadFile(csv, 'all-leads.csv');
```

### 📋 **UI Components**
```javascript
// Populate autocomplete/dropdown
const leads = await fetch('/api/v1/leads/all').then(r => r.json());
const options = leads.data.map(lead => ({
  value: lead.id,
  label: lead.lead_name
}));
```

### 🔄 **Data Synchronization**
```javascript
// Sync with external system
const leads = await fetch('/api/v1/leads/all').then(r => r.json());
await syncWithExternalSystem(leads.data);
```

### 📈 **Analytics & Dashboards**
```javascript
// Calculate statistics
const leads = await fetch('/api/v1/leads/all').then(r => r.json());
const stats = {
  total: leads.total,
  withPhone: leads.data.filter(l => l.phoneNumber).length,
  byCategory: groupBy(leads.data, 'customer_category.name'),
  byBranch: groupBy(leads.data, 'branch.name')
};
```

## Performance Considerations

### **When to Use**
- ✅ **Small to medium datasets** (< 10,000 leads)
- ✅ **Data exports** and reports
- ✅ **Populating UI components** (dropdowns, autocomplete)
- ✅ **Analytics** and dashboard calculations
- ✅ **One-time data operations**

### **When to Use Pagination Instead**
- ❌ **Large datasets** (> 10,000 leads)
- ❌ **User browsing** through leads
- ❌ **Real-time UI updates**
- ❌ **Mobile applications** with limited bandwidth

### **Performance Tips**
1. **Use search parameter** to reduce dataset size when possible
2. **Cache results** on frontend for repeated use
3. **Consider pagination** for very large datasets
4. **Monitor response times** and switch to pagination if needed

## Status Codes

- **200 OK**: Leads retrieved successfully
- **400 Bad Request**: Invalid search parameter
- **500 Internal Server Error**: Server error

## Comparison with Paginated Endpoint

| Feature | `/leads` (Paginated) | `/leads/all` (All) |
|---------|---------------------|-------------------|
| **Response Size** | Small (10-100 leads) | Large (all leads) |
| **Requests Needed** | Multiple | Single |
| **Memory Usage** | Low | High |
| **Network Overhead** | High (multiple requests) | Low (single request) |
| **Use Case** | User browsing | Data export/analysis |
| **Performance** | Consistent | Varies with dataset size |

## Error Handling

```javascript
try {
  const response = await fetch('/api/v1/leads/all?search=term');
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  const data = await response.json();
  console.log(`Retrieved ${data.total} leads`);
  
} catch (error) {
  console.error('Failed to fetch leads:', error.message);
}
```

## Security & Rate Limiting

- **Authentication**: Same requirements as other lead endpoints
- **Rate Limiting**: May have stricter limits due to larger response size
- **Data Access**: Respects user permissions and branch restrictions

The All Leads endpoint provides a powerful way to access your complete lead dataset efficiently, making it perfect for exports, analytics, and bulk operations while maintaining the same data quality and relationships as the paginated endpoint.
