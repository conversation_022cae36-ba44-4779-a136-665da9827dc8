# Users API Documentation

This document provides comprehensive documentation for the Users API endpoints that have been implemented following best practices and studying the Role, Permission, and RolePermission models.

## 📋 **Database Schema Analysis**

### **User Model Structure**
```prisma
model User {
  id                  String            @id @default(uuid())
  name                String            // Full name of the user
  email               String            // Unique email address
  phone_number        String?           // Optional phone number (international or local formats)
  password            String            // Hashed password for authentication
  rm_code             String            // Relationship Manager code
  role_id             String            // Foreign key to Role
  branch_id           String            // Foreign key to Branch
  last_login          DateTime?         // Optional last login timestamp
  created_at          DateTime          @default(now()) // Timestamp when user was created
  updated_at          DateTime          @updatedAt      // Timestamp when user was last updated

  // Relationships
  role                Role              @relation(fields: [role_id], references: [id])
  branch              Branch            @relation(fields: [branch_id], references: [id])
  
  // Activity relationships
  general_activities  GeneralActivity[]
  hitlist_calls       HitlistCallLog[]
  hitlist_assignments HitlistEntry[]    @relation("HitlistAssignedTo")
  hitlist_uploads     HitlistEntry[]    @relation("HitlistUploadedBy")
  leads               Lead[]
  loan_activities     LoanActivity[]
  scheduled_visits    ScheduledVisit[]
  targets             Target[]
}
```

### **Related Models**
- **Role**: Defines user permissions and access levels
- **Branch**: Organizational unit with regional hierarchy
- **Region**: Geographic grouping of branches

## 🚀 **API Endpoints Overview**

### **Users API** (`/api/v1/users`)

#### **Core CRUD Operations**

##### **Create User**
- **POST** `/users`
- **Body**: `{ name: string, email: string, phone_number?: string, password: string, rm_code: string, role_id: string, branch_id: string, last_login?: Date }`
- **Note**: `created_at` and `updated_at` are automatically set by the system, `password` is automatically hashed
- **Response**: User with role and branch details
- **Features**: Email uniqueness validation, role/branch existence validation

##### **List Users (Paginated)**
- **GET** `/users?page=1&limit=10&search=term`
- **Response**: Paginated list with role and branch information
- **Features**: Search across name, email, RM code, role name, branch name

##### **Get User**
- **GET** `/users/:id`
- **Response**: User details with role, branch, and activity counts
- **Features**: Complete user profile with relationship data

##### **Update User**
- **PATCH** `/users/:id`
- **Body**: `{ name?: string, email?: string, phone_number?: string, password?: string, rm_code?: string, role_id?: string, branch_id?: string, last_login?: Date }`
- **Note**: `updated_at` is automatically updated by the system on any change, `password` is automatically hashed if provided
- **Features**: Partial updates, email uniqueness validation, role/branch validation

##### **Delete User**
- **DELETE** `/users/:id`
- **Features**: Prevents deletion if user has associated activities/data

#### **Specialized Operations**

##### **Assign Role**
- **POST** `/users/:id/assign-role`
- **Body**: `{ role_id: string }`
- **Response**: Updated user with new role
- **Features**: Role existence validation, specialized role management

##### **Transfer Branch**
- **POST** `/users/:id/transfer-branch`
- **Body**: `{ branch_id: string }`
- **Response**: Updated user with new branch
- **Features**: Branch existence validation, organizational transfers

##### **Update Last Login**
- **PATCH** `/users/:id/last-login`
- **Body**: `{ last_login: Date }`
- **Features**: System endpoint for authentication tracking

##### **Change Password**
- **PATCH** `/users/:id/change-password`
- **Body**: `{ currentPassword: string, newPassword: string }`
- **Features**: Secure password change with current password verification

##### **Reset Password (Admin)**
- **PATCH** `/users/:id/reset-password`
- **Body**: `{ newPassword: string }`
- **Features**: Administrative password reset without current password verification

## 🔍 **Key Features Implemented**

### **🔒 Security & Validation**
- **Email uniqueness**: Prevents duplicate email addresses
- **Password security**: Automatic bcrypt hashing with 12 salt rounds
- **Password complexity**: Enforced strong password requirements
- **UUID validation**: All ID parameters validated as proper UUIDs
- **Phone number validation**: International (+1234567890) and local (07XXXXXXXX, 01XXXXXXXX) format validation
- **Relationship validation**: Ensures role and branch exist before assignment
- **Input sanitization**: Comprehensive validation using class-validator

### **🛡️ Data Integrity**
- **Referential integrity**: Validates foreign key relationships
- **Cascade prevention**: Prevents deletion of users with associated data
- **Transaction safety**: Consistent data state during operations
- **Conflict detection**: Handles email conflicts during updates

### **🚀 Performance Optimizations**
- **Parallel queries**: Count and data queries executed simultaneously
- **Optimized includes**: Efficient loading of related data
- **Pagination support**: Handles large datasets efficiently
- **Search optimization**: Case-insensitive search across multiple fields

### **📊 Rich Response Data**
- **Role information**: Complete role details with permissions
- **Branch hierarchy**: Branch with region information
- **Activity counts**: Statistics for all user activities
- **Relationship data**: Comprehensive user profile

### **🔧 Error Handling**
- **Detailed error messages**: Clear feedback for different scenarios
- **Database connection handling**: Graceful handling of connection issues
- **Validation errors**: Specific validation failure messages
- **Proper HTTP status codes**: RESTful error responses

## 📝 **API Usage Examples**

### **Creating a User**

#### **Example 1: User with 07XXXXXXXX Phone Number**
```bash
POST /api/v1/users
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone_number": "0712345678",
  "password": "SecurePassword123!",
  "rm_code": "RM001",
  "role_id": "550e8400-e29b-41d4-a716-446655440000",
  "branch_id": "550e8400-e29b-41d4-a716-446655440001"
}
```

#### **Example 2: User with 01XXXXXXXX Phone Number**
```bash
POST /api/v1/users
Content-Type: application/json

{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "phone_number": "0123456789",
  "password": "SecurePassword123!",
  "rm_code": "RM002",
  "role_id": "550e8400-e29b-41d4-a716-446655440000",
  "branch_id": "550e8400-e29b-41d4-a716-446655440001"
}
```

#### **Example 3: User with International Phone Number**
```bash
POST /api/v1/users
Content-Type: application/json

{
  "name": "Mike Johnson",
  "email": "<EMAIL>",
  "phone_number": "+1234567890",
  "password": "SecurePassword123!",
  "rm_code": "RM003",
  "role_id": "550e8400-e29b-41d4-a716-446655440000",
  "branch_id": "550e8400-e29b-41d4-a716-446655440001"
}
```

**Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440003",
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone_number": "0712345678",
  "rm_code": "RM001",
  "last_login": null,
  "created_at": "2024-01-15T08:00:00.000Z",
  "updated_at": "2024-01-15T08:00:00.000Z",
  "role": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "name": "Manager",
    "description": "Branch manager with team oversight"
  },
  "branch": {
    "id": "550e8400-e29b-41d4-a716-446655440001",
    "name": "Downtown Branch",
    "region": {
      "id": "550e8400-e29b-41d4-a716-446655440002",
      "name": "North Region"
    }
  },
  "generalActivitiesCount": 0,
  "leadsCount": 0,
  "loanActivitiesCount": 0,
  "scheduledVisitsCount": 0,
  "targetsCount": 0
}
```

### **Searching Users**
```bash
# Search by name
GET /api/v1/users?search=john&limit=20

# Search by email
GET /api/v1/users?search=@company.com&page=2

# Search by role
GET /api/v1/users?search=manager
```

### **Role Assignment**
```bash
POST /api/v1/users/550e8400-e29b-41d4-a716-446655440003/assign-role
Content-Type: application/json

{
  "role_id": "550e8400-e29b-41d4-a716-446655440004"
}
```

### **Branch Transfer**
```bash
POST /api/v1/users/550e8400-e29b-41d4-a716-446655440003/transfer-branch
Content-Type: application/json

{
  "branch_id": "550e8400-e29b-41d4-a716-446655440005"
}
```

### **Password Management**
```bash
# Change password (requires current password)
PATCH /api/v1/users/550e8400-e29b-41d4-a716-446655440003/change-password
Content-Type: application/json

{
  "currentPassword": "OldPassword123!",
  "newPassword": "NewSecurePassword123!"
}

# Reset password (admin operation)
PATCH /api/v1/users/550e8400-e29b-41d4-a716-446655440003/reset-password
Content-Type: application/json

{
  "newPassword": "AdminResetPassword123!"
}
```

## 🎯 **Integration with Role-Based Access Control**

### **Role Assignment Workflow**
1. **Create User**: Assign initial role during user creation
2. **Role Management**: Use specialized role assignment endpoint
3. **Permission Inheritance**: User inherits all role permissions
4. **Role Validation**: System validates role exists before assignment

### **Branch Management**
1. **Initial Assignment**: Assign branch during user creation
2. **Transfers**: Use specialized branch transfer endpoint
3. **Regional Hierarchy**: Automatic region information inclusion
4. **Organizational Structure**: Maintains proper branch relationships

### **Activity Tracking**
- **Login Tracking**: System updates last login automatically
- **Activity Counts**: Real-time counts of user activities
- **Data Relationships**: Comprehensive tracking of user data

## 📊 **Response Structure**

### **User Response DTO**
```typescript
interface UserResponseDto {
  id: string;                    // User UUID
  name: string;                  // Full name
  email: string;                 // Email address
  phone_number?: string;         // Phone number (international +1234567890 or local 07XXXXXXXX/01XXXXXXXX)
  rm_code: string;               // RM code
  last_login?: Date;             // Last login timestamp
  created_at: Date;              // Creation timestamp
  updated_at: Date;              // Last update timestamp
  role: RoleInfoDto;             // Role information
  branch: BranchInfoDto;         // Branch with region
  generalActivitiesCount: number; // Activity statistics
  leadsCount: number;
  loanActivitiesCount: number;
  scheduledVisitsCount: number;
  targetsCount: number;
  // Note: password is never included in responses for security
}
```

## ✅ **Best Practices Implemented**

1. **RESTful Design** - Consistent URL patterns and HTTP methods
2. **Comprehensive Validation** - Input validation at multiple levels
3. **Error Handling** - Detailed error responses with proper status codes
4. **Documentation** - Complete Swagger/OpenAPI documentation
5. **Testing** - Unit tests for services and controllers
6. **Performance** - Optimized database queries and pagination
7. **Security** - Input sanitization and validation
8. **Maintainability** - Well-structured, commented code
9. **Scalability** - Efficient handling of large datasets
10. **Integration** - Seamless integration with role and branch systems

The Users API provides a complete foundation for user management within the role-based access control system, with comprehensive CRUD operations and specialized endpoints for organizational management.
