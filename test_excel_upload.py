#!/usr/bin/env python3
"""
Simple Python script to test Excel upload API endpoint
Usage: python test_excel_upload.py path/to/your/excel-file.xlsx
"""

import requests
import sys
import json
import os

def test_excel_upload(file_path, server_url="http://localhost:3000/api/v1/leads/upload-excel"):
    """Test the Excel upload endpoint"""
    
    print(f"🧪 Testing Excel Upload API")
    print(f"📁 File: {file_path}")
    print(f"🌐 Server: {server_url}")
    print("-" * 50)
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"❌ Error: File '{file_path}' not found!")
        return
    
    # Check file extension
    if not file_path.lower().endswith(('.xlsx', '.xls')):
        print(f"❌ Error: File must be .xlsx or .xls format!")
        return
    
    try:
        # Prepare file for upload
        with open(file_path, 'rb') as file:
            files = {'file': (os.path.basename(file_path), file, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            
            print(f"📤 Uploading file...")
            
            # Send POST request
            response = requests.post(server_url, files=files, timeout=30)
            
            print(f"📡 Response Status: {response.status_code}")
            
            if response.status_code == 200 or response.status_code == 201:
                data = response.json()
                
                print(f"\n✅ Upload Successful!")
                print(f"📊 Summary:")
                print(f"  - Total rows: {data.get('totalRows', 'N/A')}")
                print(f"  - Processed rows: {data.get('processedRows', 'N/A')}")
                print(f"  - Successful creations: {data.get('successfulCreations', 'N/A')}")
                print(f"  - Failed creations: {data.get('failedCreations', 'N/A')}")
                
                # Show column mappings
                if 'columnMappings' in data:
                    print(f"\n🗂️ Column Mappings:")
                    for excel_col, field_name in data['columnMappings'].items():
                        print(f"  \"{excel_col}\" → {field_name}")
                
                # Show created leads
                if 'createdLeads' in data and data['createdLeads']:
                    print(f"\n✅ Created Leads:")
                    for i, lead in enumerate(data['createdLeads'][:5], 1):  # Show first 5
                        phone = lead.get('phoneNumber', 'No phone')
                        print(f"  {i}. {lead.get('lead_name', 'Unknown')} ({phone})")
                    
                    if len(data['createdLeads']) > 5:
                        print(f"  ... and {len(data['createdLeads']) - 5} more")
                
                # Show errors
                if 'errors' in data and data['errors']:
                    print(f"\n❌ Errors:")
                    for error in data['errors'][:5]:  # Show first 5 errors
                        print(f"  Row {error.get('row', '?')}: {error.get('error', 'Unknown error')}")
                    
                    if len(data['errors']) > 5:
                        print(f"  ... and {len(data['errors']) - 5} more errors")
                
                print(f"\n🎉 Test completed successfully!")
                
            else:
                print(f"\n❌ Upload Failed!")
                try:
                    error_data = response.json()
                    print(f"Error: {error_data.get('message', 'Unknown error')}")
                except:
                    print(f"Error: {response.text}")
    
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection Error: Could not connect to server!")
        print(f"💡 Make sure your server is running: npm run start:dev")
    except requests.exceptions.Timeout:
        print(f"❌ Timeout Error: Request took too long!")
    except Exception as e:
        print(f"❌ Unexpected Error: {str(e)}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python test_excel_upload.py <path-to-excel-file>")
        print("Example: python test_excel_upload.py leads.xlsx")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    # Try different ports
    ports = [3000, 3001]
    for port in ports:
        server_url = f"http://localhost:{port}/api/v1/leads/upload-excel"
        try:
            # Test if server is running
            test_response = requests.get(f"http://localhost:{port}/api/v1/leads?limit=1", timeout=5)
            if test_response.status_code in [200, 401]:  # 401 might be auth required, but server is running
                print(f"✅ Server detected on port {port}")
                test_excel_upload(file_path, server_url)
                return
        except:
            continue
    
    print("❌ No server detected on ports 3000 or 3001")
    print("💡 Start your server with: npm run start:dev")

if __name__ == "__main__":
    main()
