# Excel Upload API Documentation

## Overview

The Excel Upload API allows you to upload Excel files (.xlsx, .xls) containing lead data and automatically create leads in the database. The system intelligently parses the Excel file, maps columns to lead fields, and uses the same robust bulk creation logic with automatic foreign key handling.

## Endpoint

**POST** `/api/v1/leads/upload-excel`

## Features

- **Excel File Parsing**: Supports .xlsx and .xls formats
- **Intelligent Column Mapping**: Automatically maps Excel columns to lead fields using fuzzy matching
- **Automatic Foreign Key Resolution**: Same as bulk creation - creates missing entities automatically
- **Robust Error Handling**: Detailed error reporting with row numbers and specific issues
- **Partial Success**: Processes all rows even if some fail
- **File Validation**: Validates file format and size (10MB limit)
- **Parent-Child Relationships**: Optional anchor ID to create child leads under a parent lead

## Request Format

**Content-Type**: `multipart/form-data`

**Form Fields**:
- `file` (Excel file) - **Required**
- `anchorId` (string) - **Optional** - Parent lead ID to assign to all imported leads

### Example using cURL
```bash
# Without anchor ID
curl -X POST http://localhost:3001/api/v1/leads/upload-excel \
  -F "file=@leads.xlsx"

# With anchor ID (parent lead)
curl -X POST http://localhost:3001/api/v1/leads/upload-excel \
  -F "file=@leads.xlsx" \
  -F "anchorId=550e8400-e29b-41d4-a716-************"
```

### Example using JavaScript/FormData
```javascript
const formData = new FormData();
formData.append('file', excelFile);

// Optional: Add parent lead ID
if (parentLeadId) {
  formData.append('anchorId', parentLeadId);
}

const response = await fetch('/api/v1/leads/upload-excel', {
  method: 'POST',
  body: formData
});
```

## Excel File Format

### Supported Column Headers

The system uses intelligent column mapping that recognizes various header formats:

| Lead Field | Recognized Headers (case-insensitive) |
|------------|---------------------------------------|
| Customer Name | "Customer Name", "Client Name", "Lead Name", "Name" |
| Phone Number | "Phone Number", "Mobile Number", "Telephone", "Phone", "Mobile", "Contact" |
| Branch | "Branch", "Branch Name", "Office", "Location" |
| Customer Category | "Customer Category", "Category" |
| Employer | "Employer", "Company", "Organization", "Workplace" |
| ISIC Sector | "ISIC Sector", "Sector", "Industry", "Business Sector" |
| Lead Type | "Lead Type", "Type of Lead" |
| Status | "Status", "Lead Status", "Stage" |
| Contact Person | "Contact Person", "Contact Name", "Representative" |
| Contact Phone | "Contact Phone", "Contact Number", "Representative Phone" |
| Client ID | "Client ID", "Customer ID", "Reference ID", "ID" |

### Sample Excel Format

| Customer Name | Phone Number | Branch | Customer Category | Employer | ISIC Sector | Lead Type | Status | Contact Person | Contact Phone | Client ID |
|---------------|--------------|--------|-------------------|----------|-------------|-----------|--------|----------------|---------------|-----------|
| John Doe | +************ | Nairobi Branch | Individual | ABC Company | Technology | New | Pending | Jane Smith | +254700000011 | CLI-001 |
| Mary Johnson | 0700000002 | Mombasa Branch | Corporate | XYZ Ltd | Manufacturing | Existing | Hot | Bob Wilson | 0700000012 | CLI-002 |

## Response Format

```json
{
  "success": boolean,
  "message": string,
  "totalRows": number,
  "processedRows": number,
  "successfulCreations": number,
  "failedCreations": number,
  "createdLeads": LeadSummaryResponseDto[],
  "errors": Array<{
    "row": number,
    "error": string,
    "data": object
  }>,
  "columnMappings": Record<string, string>
}
```

### Response Example

```json
{
  "success": false,
  "message": "Successfully processed 5 rows from Excel file",
  "totalRows": 6,
  "processedRows": 5,
  "successfulCreations": 4,
  "failedCreations": 1,
  "createdLeads": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "anchor_name": null,
      "lead_name": "John Doe",
      "status": "pending",
      "phoneNumber": "+************",
      "no_of_visits": 0,
      "no_of_calls": 0,
      "last_interaction": null,
      "customer_category": null,
      "isic_sector": null,
      "branch": null
    }
  ],
  "errors": [
    {
      "row": 3,
      "error": "Client ID 'CLI-001' already exists",
      "data": {
        "customerName": "Duplicate Customer",
        "clientId": "CLI-001"
      }
    }
  ],
  "columnMappings": {
    "Customer Name": "customerName",
    "Phone Number": "phoneNumber",
    "Branch": "branchIdentifier"
  }
}
```

## Anchor ID (Parent Lead Relationships)

### Overview
The `anchorId` parameter allows you to create parent-child relationships between leads. When provided, all leads imported from the Excel file will have their `parent_lead_id` set to the specified anchor ID.

### Use Cases
- **Referral Programs**: Import leads referred by a specific customer
- **Campaign Tracking**: Group leads from a specific marketing campaign
- **Hierarchical Organization**: Create sub-leads under a main lead
- **Bulk Child Creation**: Add multiple child leads to an existing parent

### Requirements
- **Format**: Must be a valid UUID (e.g., `550e8400-e29b-41d4-a716-************`)
- **Validation**: The parent lead must exist in the database
- **Optional**: Can be omitted to create independent leads

### Example Scenarios

**Scenario 1: Referral Import**
```bash
# Customer John Doe (ID: abc-123) referred 5 new leads
curl -X POST http://localhost:3001/api/v1/leads/upload-excel \
  -F "file=@referrals-from-john.xlsx" \
  -F "anchorId=abc-123"
```

**Scenario 2: Campaign Leads**
```bash
# Import leads from a marketing campaign under campaign lead
curl -X POST http://localhost:3001/api/v1/leads/upload-excel \
  -F "file=@campaign-leads.xlsx" \
  -F "anchorId=campaign-lead-id"
```

### Error Handling
- **Invalid UUID**: Returns 400 Bad Request with validation error
- **Non-existent Parent**: Returns 400 Bad Request with "Parent lead not found" message
- **Missing Parent**: If anchorId is provided but parent doesn't exist, upload fails completely

## Phone Number Formats

The system automatically normalizes phone numbers to international format:

- **Input**: `+************` → **Output**: `+************`
- **Input**: `************` → **Output**: `+************`
- **Input**: `0700000001` → **Output**: `+************`
- **Input**: `0100000001` → **Output**: `+************`
- **Input**: `700000001` → **Output**: `+************`

## Foreign Key Handling

Same as the bulk creation endpoint:

### Branch
- **UUID**: Validates existence
- **Name**: Finds existing or creates new branch with default region

### Customer Category
- **UUID**: Validates existence
- **Name**: Finds existing or creates new category

### Employer
- **UUID**: Validates existence
- **Name**: Finds existing or creates new employer

### ISIC Sector
- **UUID**: Validates existence
- **Name**: Finds existing or creates new sector

## Error Types

### File Validation Errors
- **Invalid file format**: Only .xlsx and .xls files allowed
- **File too large**: Maximum 10MB file size
- **Empty file**: File contains no data
- **No sheets**: Excel file contains no worksheets

### Parsing Errors
- **Row parsing error**: Invalid data in specific row
- **Missing required data**: Critical fields missing

### Creation Errors
- **Duplicate client ID**: Client ID already exists in database
- **Invalid UUID**: Provided UUID doesn't exist
- **Validation errors**: Field validation failures

## Status Codes

- **201 Created**: File processed successfully (even with partial failures)
- **400 Bad Request**: Invalid file format, file too large, or parsing errors
- **409 Conflict**: Some leads could not be created due to conflicts

## Best Practices

### Excel File Preparation
1. **Use clear headers**: Use standard column names for better mapping
2. **Consistent data format**: Keep phone numbers and other data consistent
3. **Unique client IDs**: Ensure client IDs are unique if provided
4. **Clean data**: Remove empty rows and invalid characters

### Error Handling
1. **Check response**: Always check the `success` field and `errors` array
2. **Display errors**: Show specific row errors to users for correction
3. **Retry mechanism**: Allow users to fix errors and re-upload
4. **Partial success**: Handle cases where some leads succeed and others fail

### Performance Considerations
- **File size**: Keep files under 10MB for optimal performance
- **Batch size**: System processes in batches of 10 leads
- **Large files**: For very large files, consider splitting into smaller chunks

## Integration Example

```javascript
async function uploadExcelFile(file) {
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('/api/v1/leads/upload-excel', {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log(`Successfully created ${result.successfulCreations} leads`);
    } else {
      console.log(`Created ${result.successfulCreations} leads with ${result.failedCreations} errors`);
      
      // Display errors to user
      result.errors.forEach(error => {
        console.error(`Row ${error.row}: ${error.error}`);
      });
    }
    
    return result;
    
  } catch (error) {
    console.error('Upload failed:', error);
    throw error;
  }
}
```

## Troubleshooting

### Common Issues

1. **Column not mapped**: Check if column header matches recognized patterns
2. **Phone number format**: Ensure phone numbers are in valid format
3. **Duplicate errors**: Check for existing client IDs in database
4. **File format**: Ensure file is .xlsx or .xls format
5. **Empty rows**: Remove empty rows from Excel file

### Debug Information

The response includes `columnMappings` to show how Excel columns were mapped to lead fields. Use this to verify correct mapping and adjust column headers if needed.

## Security Considerations

- **File size limit**: 10MB maximum to prevent abuse
- **File type validation**: Only Excel files accepted
- **Input validation**: All data validated before database insertion
- **SQL injection protection**: Uses Prisma ORM for safe database operations
