<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            background-color: #e3f2fd;
            border-color: #0056b3;
        }
        input[type="file"] {
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 100%;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .sample-format {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Excel Upload API Test</h1>
        <p>Test the <code>POST /api/v1/leads/upload-excel</code> endpoint</p>
        
        <div class="sample-format">
            <h3>📋 Expected Excel Format:</h3>
            <table>
                <tr>
                    <th>Customer Name</th>
                    <th>Phone Number</th>
                    <th>Branch</th>
                    <th>Customer Category</th>
                    <th>Employer</th>
                    <th>ISIC Sector</th>
                    <th>Lead Type</th>
                    <th>Status</th>
                </tr>
                <tr>
                    <td>John Doe</td>
                    <td>+254700000001</td>
                    <td>Nairobi Branch</td>
                    <td>Individual</td>
                    <td>ABC Company</td>
                    <td>Technology</td>
                    <td>New</td>
                    <td>Pending</td>
                </tr>
                <tr>
                    <td>Jane Smith</td>
                    <td>0700000002</td>
                    <td>Mombasa Branch</td>
                    <td>Corporate</td>
                    <td>XYZ Ltd</td>
                    <td>Manufacturing</td>
                    <td>Existing</td>
                    <td>Hot</td>
                </tr>
            </table>
            <p><strong>Note:</strong> All columns are optional. The system will auto-create missing entities.</p>
        </div>

        <div class="upload-area" id="uploadArea">
            <h3>📁 Select Excel File</h3>
            <p>Drag and drop your Excel file here, or click to select</p>
            <input type="file" id="fileInput" accept=".xlsx,.xls" />
        </div>

        <div>
            <label for="serverUrl">🌐 Server URL:</label>
            <input type="text" id="serverUrl" value="http://localhost:3000/api/v1/leads/upload-excel"
                   style="width: 100%; padding: 8px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px;">
        </div>

        <div>
            <label for="anchorId">👨‍👩‍👧‍👦 Anchor ID (Optional Parent Lead ID):</label>
            <input type="text" id="anchorId" placeholder="e.g., 550e8400-e29b-41d4-a716-446655440000"
                   style="width: 100%; padding: 8px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px;">
            <small style="color: #666;">If provided, all imported leads will be assigned this parent_lead_id</small>
        </div>

        <button onclick="uploadFile()" id="uploadBtn">📤 Upload Excel File</button>
        <button onclick="clearResult()">🧹 Clear Result</button>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Uploading and processing Excel file...</p>
        </div>

        <div id="result"></div>
    </div>

    <script>
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const uploadBtn = document.getElementById('uploadBtn');
        const loading = document.getElementById('loading');
        const result = document.getElementById('result');
        const serverUrl = document.getElementById('serverUrl');

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                showFileInfo(files[0]);
            }
        });

        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                showFileInfo(e.target.files[0]);
            }
        });

        function showFileInfo(file) {
            const info = `Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
            showResult(info, 'info');
        }

        async function uploadFile() {
            const file = fileInput.files[0];
            const anchorId = document.getElementById('anchorId').value.trim();

            if (!file) {
                showResult('❌ Please select an Excel file first!', 'error');
                return;
            }

            if (!file.name.match(/\.(xlsx|xls)$/)) {
                showResult('❌ Please select a valid Excel file (.xlsx or .xls)', 'error');
                return;
            }

            // Validate anchorId format if provided
            if (anchorId && !anchorId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
                showResult('❌ Anchor ID must be a valid UUID format', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            // Add anchorId if provided
            if (anchorId) {
                formData.append('anchorId', anchorId);
            }

            uploadBtn.disabled = true;
            loading.style.display = 'block';
            result.innerHTML = '';

            try {
                const response = await fetch(serverUrl.value, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    let resultText = `✅ Upload Successful!\n\n`;
                    resultText += `📊 Summary:\n`;
                    resultText += `- Total rows: ${data.totalRows}\n`;
                    resultText += `- Processed rows: ${data.processedRows}\n`;
                    resultText += `- Successful creations: ${data.successfulCreations}\n`;
                    resultText += `- Failed creations: ${data.failedCreations}\n`;

                    if (anchorId) {
                        resultText += `- Parent Lead ID: ${anchorId}\n`;
                    }
                    resultText += '\n';

                    if (data.columnMappings) {
                        resultText += `🗂️ Column Mappings:\n`;
                        Object.entries(data.columnMappings).forEach(([excel, field]) => {
                            resultText += `  "${excel}" → ${field}\n`;
                        });
                        resultText += '\n';
                    }

                    if (data.createdLeads && data.createdLeads.length > 0) {
                        resultText += `✅ Created Leads:\n`;
                        data.createdLeads.forEach((lead, i) => {
                            resultText += `  ${i + 1}. ${lead.lead_name} (${lead.phoneNumber || 'No phone'})\n`;
                        });
                        resultText += '\n';
                    }

                    if (data.errors && data.errors.length > 0) {
                        resultText += `❌ Errors:\n`;
                        data.errors.forEach(error => {
                            resultText += `  Row ${error.row}: ${error.error}\n`;
                        });
                    }

                    showResult(resultText, data.success ? 'success' : 'error');
                } else {
                    showResult(`❌ Error: ${data.message || 'Upload failed'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Network Error: ${error.message}\n\nMake sure your server is running on the correct port!`, 'error');
            } finally {
                uploadBtn.disabled = false;
                loading.style.display = 'none';
            }
        }

        function showResult(text, type) {
            result.innerHTML = text;
            result.className = `result ${type}`;
        }

        function clearResult() {
            result.innerHTML = '';
            result.className = 'result';
            fileInput.value = '';
        }

        // Check if server is running on page load
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('http://localhost:3000/api/v1/leads?limit=1');
                if (response.ok) {
                    showResult('✅ Server is running on port 3000', 'success');
                }
            } catch (error) {
                try {
                    const response = await fetch('http://localhost:3001/api/v1/leads?limit=1');
                    if (response.ok) {
                        serverUrl.value = 'http://localhost:3001/api/v1/leads/upload-excel';
                        showResult('✅ Server is running on port 3001', 'success');
                    }
                } catch (error2) {
                    showResult('⚠️ Server not detected. Make sure to start your server with: npm run start:dev', 'error');
                }
            }
        });
    </script>
</body>
</html>
