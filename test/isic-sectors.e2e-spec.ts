import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/prisma/prisma.service';

describe('ISIC Sectors (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    prisma = moduleFixture.get<PrismaService>(PrismaService);

    // Set global prefix to match main.ts
    app.setGlobalPrefix('api/v1');

    await app.init();
  });

  afterEach(async () => {
    // Clean up test data
    await prisma.iSICSector.deleteMany({
      where: {
        name: {
          in: [
            'Test Sector',
            'Another Test Sector',
            'Manufacturing Sector',
            'First Sector',
            'Second Sector',
            'Updated Test Sector',
            'Agriculture and Forestry',
          ],
        },
      },
    });
    await app.close();
  });

  describe('/api/v1/isic-sectors (POST)', () => {
    it('should create a new ISIC sector with name only', () => {
      return request(app.getHttpServer())
        .post('/api/v1/isic-sectors')
        .send({ name: 'Test Sector' })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body).toHaveProperty('code', '');
          expect(res.body).toHaveProperty('name', 'Test Sector');
          expect(res.body).toHaveProperty('addedOnDate');
          expect(res.body).toHaveProperty('addedBy', '');
        });
    });

    it('should create a new ISIC sector with name and code', () => {
      return request(app.getHttpServer())
        .post('/api/v1/isic-sectors')
        .send({ name: 'Manufacturing Sector', code: 'C10' })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body).toHaveProperty('code', 'C10');
          expect(res.body).toHaveProperty('name', 'Manufacturing Sector');
          expect(res.body).toHaveProperty('addedOnDate');
          expect(res.body).toHaveProperty('addedBy', '');
        });
    });

    it('should return 409 when creating sector with duplicate name', async () => {
      // First create a sector
      await request(app.getHttpServer())
        .post('/api/v1/isic-sectors')
        .send({ name: 'Test Sector' })
        .expect(201);

      // Try to create another with same name
      return request(app.getHttpServer())
        .post('/api/v1/isic-sectors')
        .send({ name: 'Test Sector' })
        .expect(409)
        .expect((res) => {
          expect(res.body.message).toContain('already exists');
        });
    });

    it('should return 409 when creating sector with duplicate code', async () => {
      // First create a sector with code
      await request(app.getHttpServer())
        .post('/api/v1/isic-sectors')
        .send({ name: 'First Sector', code: 'A01' })
        .expect(201);

      // Try to create another with same code
      return request(app.getHttpServer())
        .post('/api/v1/isic-sectors')
        .send({ name: 'Second Sector', code: 'A01' })
        .expect(409)
        .expect((res) => {
          expect(res.body.message).toContain('already exists');
        });
    });

    it('should return 400 when name is missing', () => {
      return request(app.getHttpServer())
        .post('/api/v1/isic-sectors')
        .send({})
        .expect(400);
    });
  });

  describe('/api/v1/isic-sectors (GET)', () => {
    it('should return paginated list of ISIC sectors', async () => {
      // Create test data
      await request(app.getHttpServer())
        .post('/api/v1/isic-sectors')
        .send({ name: 'Test Sector' })
        .expect(201);

      return request(app.getHttpServer())
        .get('/api/v1/isic-sectors')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('data');
          expect(res.body).toHaveProperty('meta');
          expect(Array.isArray(res.body.data)).toBe(true);
          expect(res.body.meta).toHaveProperty('total');
          expect(res.body.meta).toHaveProperty('page');
          expect(res.body.meta).toHaveProperty('limit');
        });
    });
  });

  describe('/api/v1/isic-sectors/:id (PATCH)', () => {
    it('should update ISIC sector name only', async () => {
      // Create a sector first
      const createResponse = await request(app.getHttpServer())
        .post('/api/v1/isic-sectors')
        .send({ name: 'Test Sector' })
        .expect(201);

      const sectorId = createResponse.body.id;

      // Update only the name
      return request(app.getHttpServer())
        .patch(`/api/v1/isic-sectors/${sectorId}`)
        .send({ name: 'Updated Test Sector' })
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe(sectorId);
          expect(res.body.name).toBe('Updated Test Sector');
          expect(res.body.code).toBe('');
          expect(res.body).toHaveProperty('addedOnDate');
          expect(res.body).toHaveProperty('addedBy', '');
        });
    });

    it('should update ISIC sector code only', async () => {
      // Create a sector first
      const createResponse = await request(app.getHttpServer())
        .post('/api/v1/isic-sectors')
        .send({ name: 'Another Test Sector' })
        .expect(201);

      const sectorId = createResponse.body.id;

      // Update only the code
      return request(app.getHttpServer())
        .patch(`/api/v1/isic-sectors/${sectorId}`)
        .send({ code: 'A01' })
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe(sectorId);
          expect(res.body.name).toBe('Another Test Sector');
          expect(res.body.code).toBe('A01');
        });
    });

    it('should update both code and name', async () => {
      // Create a sector first
      const createResponse = await request(app.getHttpServer())
        .post('/api/v1/isic-sectors')
        .send({ name: 'Test Sector' })
        .expect(201);

      const sectorId = createResponse.body.id;

      // Update both fields
      return request(app.getHttpServer())
        .patch(`/api/v1/isic-sectors/${sectorId}`)
        .send({ code: 'B02', name: 'Agriculture and Forestry' })
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe(sectorId);
          expect(res.body.name).toBe('Agriculture and Forestry');
          expect(res.body.code).toBe('B02');
        });
    });

    it('should return existing sector when no fields provided', async () => {
      // Create a sector first
      const createResponse = await request(app.getHttpServer())
        .post('/api/v1/isic-sectors')
        .send({ name: 'Test Sector' })
        .expect(201);

      const sectorId = createResponse.body.id;

      // Update with empty body
      return request(app.getHttpServer())
        .patch(`/api/v1/isic-sectors/${sectorId}`)
        .send({})
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe(sectorId);
          expect(res.body.name).toBe('Test Sector');
          expect(res.body.code).toBe('');
        });
    });

    it('should return 409 when updating with duplicate name', async () => {
      // Create two sectors
      await request(app.getHttpServer())
        .post('/api/v1/isic-sectors')
        .send({ name: 'First Sector' })
        .expect(201);

      const secondResponse = await request(app.getHttpServer())
        .post('/api/v1/isic-sectors')
        .send({ name: 'Second Sector' })
        .expect(201);

      // Try to update second sector with first sector's name
      return request(app.getHttpServer())
        .patch(`/api/v1/isic-sectors/${secondResponse.body.id}`)
        .send({ name: 'First Sector' })
        .expect(409)
        .expect((res) => {
          expect(res.body.message).toContain('already exists');
        });
    });

    it('should return 404 when updating non-existent sector', () => {
      const nonExistentId = '550e8400-e29b-41d4-a716-************';

      return request(app.getHttpServer())
        .patch(`/api/v1/isic-sectors/${nonExistentId}`)
        .send({ name: 'Non-existent' })
        .expect(404)
        .expect((res) => {
          expect(res.body.message).toContain('not found');
        });
    });
  });

  describe('/api/v1/isic-sectors/:id (DELETE)', () => {
    it('should delete an ISIC sector and return 204', async () => {
      // Create a sector first
      const createResponse = await request(app.getHttpServer())
        .post('/api/v1/isic-sectors')
        .send({ name: 'Test Sector' })
        .expect(201);

      const sectorId = createResponse.body.id;

      // Delete the sector
      return request(app.getHttpServer())
        .delete(`/api/v1/isic-sectors/${sectorId}`)
        .expect(204);
    });

    it('should return 404 when deleting non-existent sector', () => {
      const nonExistentId = '550e8400-e29b-41d4-a716-************';

      return request(app.getHttpServer())
        .delete(`/api/v1/isic-sectors/${nonExistentId}`)
        .expect(404)
        .expect((res) => {
          expect(res.body.message).toContain('not found');
        });
    });

    it('should return 400 when ID is not a valid UUID', () => {
      return request(app.getHttpServer())
        .delete('/api/v1/isic-sectors/invalid-id')
        .expect(400);
    });
  });
});
