# Bulk Permission Creation Guide

This guide demonstrates how to create many permissions at once using the new bulk creation endpoint, perfect for initial system setup and administrative tasks.

## 🚀 **New Bulk Creation Endpoint**

### **Endpoint**: `POST /api/v1/permissions/bulk`

Create multiple permissions in a single API call with detailed results about what was created, skipped, or failed.

## 📋 **API Usage Examples**

### **Basic Bulk Creation**

```bash
POST /api/v1/permissions/bulk
Content-Type: application/json

{
  "permissions": [
    {
      "id": "users.create",
      "name": "Create Users",
      "description": "Create new user accounts"
    },
    {
      "id": "users.read",
      "name": "View Users",
      "description": "View user profiles and lists"
    },
    {
      "id": "users.update",
      "name": "Update Users",
      "description": "Edit existing user accounts"
    },
    {
      "id": "users.delete",
      "name": "Delete Users",
      "description": "Remove user accounts"
    }
  ]
}
```

### **Response Format**

```json
{
  "created": [
    {
      "id": "users.create",
      "name": "Create Users",
      "description": "Create new user accounts",
      "roleCount": 0
    },
    {
      "id": "users.read",
      "name": "View Users", 
      "description": "View user profiles and lists",
      "roleCount": 0
    },
    {
      "id": "users.update",
      "name": "Update Users",
      "description": "Edit existing user accounts",
      "roleCount": 0
    },
    {
      "id": "users.delete",
      "name": "Delete Users",
      "description": "Remove user accounts",
      "roleCount": 0
    }
  ],
  "skipped": [],
  "errors": []
}
```

## 🏗️ **Complete System Setup Example**

### **Create All Permissions for a Complete System**

```bash
POST /api/v1/permissions/bulk
{
  "permissions": [
    // User Management
    { "id": "users.create", "name": "Create Users", "description": "Create new user accounts" },
    { "id": "users.read", "name": "View Users", "description": "View user profiles and lists" },
    { "id": "users.update", "name": "Update Users", "description": "Edit existing user accounts" },
    { "id": "users.delete", "name": "Delete Users", "description": "Remove user accounts" },
    { "id": "users.manage", "name": "Manage Users", "description": "Full user management access" },
    
    // Content Management
    { "id": "content.create", "name": "Create Content", "description": "Create new content items" },
    { "id": "content.read", "name": "View Content", "description": "View content items" },
    { "id": "content.update", "name": "Edit Content", "description": "Edit existing content" },
    { "id": "content.delete", "name": "Delete Content", "description": "Remove content items" },
    { "id": "content.publish", "name": "Publish Content", "description": "Publish content to live site" },
    { "id": "content.moderate", "name": "Moderate Content", "description": "Review and approve content" },
    
    // Reporting & Analytics
    { "id": "reports.generate", "name": "Generate Reports", "description": "Create system reports" },
    { "id": "reports.export", "name": "Export Reports", "description": "Export reports to various formats" },
    { "id": "reports.schedule", "name": "Schedule Reports", "description": "Set up automated reports" },
    { "id": "analytics.view", "name": "View Analytics", "description": "Access analytics dashboard" },
    
    // System Administration
    { "id": "admin.settings", "name": "System Settings", "description": "Modify system configuration" },
    { "id": "admin.logs", "name": "View Logs", "description": "Access system logs" },
    { "id": "admin.backup", "name": "Backup System", "description": "Create and restore backups" },
    { "id": "admin.maintenance", "name": "System Maintenance", "description": "Perform maintenance tasks" },
    
    // Role & Permission Management
    { "id": "roles.create", "name": "Create Roles", "description": "Create new user roles" },
    { "id": "roles.read", "name": "View Roles", "description": "View existing roles" },
    { "id": "roles.update", "name": "Update Roles", "description": "Modify role permissions" },
    { "id": "roles.delete", "name": "Delete Roles", "description": "Remove roles from system" },
    { "id": "permissions.manage", "name": "Manage Permissions", "description": "Create and modify permissions" },
    
    // Financial/Billing
    { "id": "billing.view", "name": "View Billing", "description": "Access billing information" },
    { "id": "billing.manage", "name": "Manage Billing", "description": "Modify billing settings" },
    { "id": "payments.process", "name": "Process Payments", "description": "Handle payment transactions" },
    
    // API Access
    { "id": "api.read", "name": "API Read Access", "description": "Read data via API" },
    { "id": "api.write", "name": "API Write Access", "description": "Modify data via API" },
    { "id": "api.admin", "name": "API Admin Access", "description": "Full API administrative access" }
  ]
}
```

## 🔄 **Handling Duplicates and Errors**

### **Response with Mixed Results**

```json
{
  "created": [
    {
      "id": "content.create",
      "name": "Create Content",
      "description": "Create new content items",
      "roleCount": 0
    },
    {
      "id": "content.update",
      "name": "Edit Content", 
      "description": "Edit existing content",
      "roleCount": 0
    }
  ],
  "skipped": [
    "Permission ID 'users.create' already exists",
    "Permission name 'View Users' already exists"
  ],
  "errors": [
    "Failed to create permission 'invalid-id': Permission ID must be lowercase, start with a letter, and use dots for hierarchy"
  ]
}
```

## 💻 **Frontend Implementation**

### **JavaScript/TypeScript Example**

```typescript
interface BulkCreateResult {
  created: PermissionResponseDto[];
  skipped: string[];
  errors: string[];
}

async function createSystemPermissions(): Promise<BulkCreateResult> {
  const permissions = [
    // User permissions
    { id: 'users.create', name: 'Create Users', description: 'Create new user accounts' },
    { id: 'users.read', name: 'View Users', description: 'View user profiles and lists' },
    { id: 'users.update', name: 'Update Users', description: 'Edit existing user accounts' },
    { id: 'users.delete', name: 'Delete Users', description: 'Remove user accounts' },
    
    // Content permissions
    { id: 'content.create', name: 'Create Content', description: 'Create new content items' },
    { id: 'content.publish', name: 'Publish Content', description: 'Publish content to live site' },
    
    // Admin permissions
    { id: 'admin.settings', name: 'System Settings', description: 'Modify system configuration' },
    { id: 'admin.logs', name: 'View Logs', description: 'Access system logs' },
  ];

  const response = await fetch('/api/v1/permissions/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ permissions }),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
}

// Usage
createSystemPermissions()
  .then(result => {
    console.log(`✅ Created ${result.created.length} permissions`);
    console.log(`⏭️ Skipped ${result.skipped.length} duplicates`);
    console.log(`❌ ${result.errors.length} errors occurred`);
    
    if (result.errors.length > 0) {
      console.error('Errors:', result.errors);
    }
  })
  .catch(error => {
    console.error('Failed to create permissions:', error);
  });
```

### **React Component Example**

```jsx
function BulkPermissionCreator() {
  const [permissions, setPermissions] = useState([
    { id: '', name: '', description: '' }
  ]);
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);

  const addPermission = () => {
    setPermissions([...permissions, { id: '', name: '', description: '' }]);
  };

  const updatePermission = (index, field, value) => {
    const updated = [...permissions];
    updated[index][field] = value;
    setPermissions(updated);
  };

  const removePermission = (index) => {
    setPermissions(permissions.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const response = await fetch('/api/v1/permissions/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          permissions: permissions.filter(p => p.id && p.name) 
        }),
      });
      
      const result = await response.json();
      setResult(result);
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h2>Bulk Create Permissions</h2>
      
      <form onSubmit={handleSubmit}>
        {permissions.map((permission, index) => (
          <div key={index} className="permission-row">
            <input
              placeholder="Permission ID (e.g., users.create)"
              value={permission.id}
              onChange={(e) => updatePermission(index, 'id', e.target.value)}
            />
            <input
              placeholder="Permission Name"
              value={permission.name}
              onChange={(e) => updatePermission(index, 'name', e.target.value)}
            />
            <input
              placeholder="Description (optional)"
              value={permission.description}
              onChange={(e) => updatePermission(index, 'description', e.target.value)}
            />
            <button type="button" onClick={() => removePermission(index)}>
              Remove
            </button>
          </div>
        ))}
        
        <button type="button" onClick={addPermission}>
          Add Permission
        </button>
        
        <button type="submit" disabled={loading}>
          {loading ? 'Creating...' : 'Create Permissions'}
        </button>
      </form>

      {result && (
        <div className="results">
          <h3>Results</h3>
          <p>✅ Created: {result.created.length}</p>
          <p>⏭️ Skipped: {result.skipped.length}</p>
          <p>❌ Errors: {result.errors.length}</p>
          
          {result.errors.length > 0 && (
            <div className="errors">
              <h4>Errors:</h4>
              <ul>
                {result.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
```

## ✅ **Key Benefits**

1. **🚀 Efficiency**: Create dozens of permissions in one API call
2. **🛡️ Safe**: Handles duplicates gracefully without errors
3. **📊 Detailed Results**: Know exactly what was created, skipped, or failed
4. **🔄 Idempotent**: Safe to run multiple times
5. **⚡ Fast Setup**: Perfect for initial system configuration
6. **🎯 Validation**: All permissions validated before creation
7. **📝 Comprehensive**: Supports all permission fields (id, name, description)

This bulk creation feature makes it easy to set up a complete permission system quickly and efficiently!
