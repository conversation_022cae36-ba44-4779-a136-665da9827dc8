# KB Tracker API Endpoints

## Overview
This document outlines the comprehensive CRUD API endpoints created for the KB Tracker backend application.

## Base URL
```
http://localhost:3000/api/v1
```

## API Documentation
Swagger documentation is available at: `http://localhost:3000/api/docs`

## Regions API

### 1. Create Region
**POST** `/api/v1/regions`

**Request Body:**
```json
{
  "name": "Central Region"
}
```

**Response (201):**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "Central Region",
  "branchCount": 0
}
```

### 2. Get All Regions
**GET** `/api/v1/regions?page=1&limit=10&search=central`

**Response (200):**
```json
{
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Central Region",
      "branchCount": 5
    }
  ],
  "meta": {
    "total": 1,
    "page": 1,
    "limit": 10,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false
  }
}
```

### 3. Get Region by ID
**GET** `/api/v1/regions/{id}`

**Response (200):**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "Central Region",
  "branchCount": 5
}
```

### 4. Update Region
**PATCH** `/api/v1/regions/{id}`

**Request Body:**
```json
{
  "name": "Updated Central Region"
}
```

**Response (200):**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "Updated Central Region",
  "branchCount": 5
}
```

### 5. Delete Region
**DELETE** `/api/v1/regions/{id}`

**Response (204):** No content

### 6. Get Region Branches
**GET** `/api/v1/regions/{id}/branches?page=1&limit=10&search=main`

**Response (200):**
```json
{
  "data": [
    {
      "id": "branch-uuid",
      "name": "Main Street Branch",
      "region_id": "550e8400-e29b-41d4-a716-************"
    }
  ],
  "meta": {
    "total": 1,
    "page": 1,
    "limit": 10,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false
  }
}
```

## Branches API

### 1. Create Branch
**POST** `/api/v1/branches`

**Request Body:**
```json
{
  "name": "Main Street Branch",
  "region_id": "550e8400-e29b-41d4-a716-************"
}
```

**Response (201):**
```json
{
  "id": "branch-uuid",
  "name": "Main Street Branch",
  "region_id": "550e8400-e29b-41d4-a716-************",
  "region": {
    "id": "550e8400-e29b-41d4-a716-************",
    "name": "Central Region"
  }
}
```

### 2. Get All Branches
**GET** `/api/v1/branches?page=1&limit=10&search=main`

**Response (200):**
```json
{
  "data": [
    {
      "id": "branch-uuid",
      "name": "Main Street Branch",
      "region_id": "550e8400-e29b-41d4-a716-************",
      "region": {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "Central Region"
      }
    }
  ],
  "meta": {
    "total": 1,
    "page": 1,
    "limit": 10,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false
  }
}
```

### 3. Get Branch by ID
**GET** `/api/v1/branches/{id}`

**Response (200):**
```json
{
  "id": "branch-uuid",
  "name": "Main Street Branch",
  "region_id": "550e8400-e29b-41d4-a716-************",
  "region": {
    "id": "550e8400-e29b-41d4-a716-************",
    "name": "Central Region"
  }
}
```

### 4. Update Branch
**PATCH** `/api/v1/branches/{id}`

**Request Body:**
```json
{
  "name": "Updated Main Street Branch",
  "region_id": "new-region-uuid"
}
```

**Response (200):**
```json
{
  "id": "branch-uuid",
  "name": "Updated Main Street Branch",
  "region_id": "new-region-uuid",
  "region": {
    "id": "new-region-uuid",
    "name": "New Region"
  }
}
```

### 5. Delete Branch
**DELETE** `/api/v1/branches/{id}`

**Response (204):** No content

## Error Responses

### 400 Bad Request
```json
{
  "statusCode": 400,
  "message": ["name should not be empty"],
  "error": "Bad Request"
}
```

### 404 Not Found
```json
{
  "statusCode": 404,
  "message": "Region with ID 'invalid-uuid' not found",
  "error": "Not Found"
}
```

### 409 Conflict
```json
{
  "statusCode": 409,
  "message": "Region with name 'Central Region' already exists",
  "error": "Conflict"
}
```

## Features Implemented

### ✅ **CRUD Operations**
- Complete Create, Read, Update, Delete operations for both Regions and Branches
- Proper HTTP status codes and response formats
- UUID-based primary keys

### ✅ **Data Validation**
- Input validation using class-validator decorators
- Type safety with TypeScript DTOs
- Automatic transformation and sanitization

### ✅ **Query Optimization**
- Efficient database queries with Prisma ORM
- Parallel query execution for better performance
- Selective field inclusion to reduce data transfer

### ✅ **Pagination & Search**
- Standardized pagination across all endpoints
- Case-insensitive search functionality
- Configurable page size with limits

### ✅ **Relationship Management**
- Proper foreign key relationships
- Cascade prevention for data integrity
- Related data inclusion in responses

### ✅ **Error Handling**
- Comprehensive error handling with meaningful messages
- Proper HTTP status codes
- Validation error details

### ✅ **API Documentation**
- Complete Swagger/OpenAPI documentation
- Interactive API explorer
- Request/response examples

### ✅ **Best Practices**
- Clean architecture with separation of concerns
- Dependency injection
- Comprehensive logging
- Type-safe database operations
