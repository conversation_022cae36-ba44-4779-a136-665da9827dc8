# Roles and Permissions API Documentation

This document provides comprehensive documentation for the Roles and Permissions API endpoints that have been implemented following best practices.

## Overview

The system implements a robust Role-Based Access Control (RBAC) system with three main components:

1. **Permissions** - Define what actions can be performed
2. **Roles** - Group permissions together for easier management
3. **Role-Permissions** - Manage the many-to-many relationship between roles and permissions

## Database Schema

### Models

```prisma
model Role {
  id               String           @id @default(uuid())
  name             String           // Non-nullable, unique role name
  description      String?          // Optional description
  role_permissions RolePermission[]
  users            User[]
}

model Permission {
  id               String           @id // Custom permission ID (e.g., "users.create", "reports.read")
  name             String           // Non-nullable, human-readable name
  description      String?          // Optional description
  role_permissions RolePermission[]
}

model RolePermission {
  role_id       String
  permission_id String             // References custom permission ID
  created_at    DateTime?  @default(now())
  permission    Permission @relation(fields: [permission_id], references: [id])
  role          Role       @relation(fields: [role_id], references: [id])
  @@id([role_id, permission_id])
}
```

## API Endpoints

### Permissions API (`/api/v1/permissions`)

#### Create Permission
- **POST** `/permissions`
- **Body**: `{ id: string, name: string, description?: string }`
- **Response**: Permission with role count
- **Features**: Validates unique IDs and names, custom ID format validation

#### Bulk Create Permissions ⭐ **NEW**
- **POST** `/permissions/bulk`
- **Body**: `{ permissions: [{ id: string, name: string, description?: string }] }`
- **Response**: `{ created: Permission[], skipped: string[], errors: string[] }`
- **Features**: Create many permissions at once, handles duplicates gracefully

#### List Permissions (Paginated)
- **GET** `/permissions?page=1&limit=10&search=term`
- **Response**: Paginated list with role counts
- **Features**: Search by name/description, pagination

#### Get All Permissions ⭐ **NEW**
- **GET** `/permissions/all?search=term`
- **Response**: All permissions without pagination
- **Features**: Perfect for checkboxes/dropdowns, optional search

#### Get Permission
- **GET** `/permissions/:id`
- **Response**: Permission details with role count

#### Update Permission
- **PATCH** `/permissions/:id`
- **Body**: `{ name?: string, description?: string }`
- **Features**: Validates unique names on update, ID is immutable

#### Delete Permission
- **DELETE** `/permissions/:id`
- **Features**: Prevents deletion if assigned to roles

#### Get Permission Roles
- **GET** `/permissions/:id/roles?page=1&limit=10&search=term`
- **Response**: Paginated list of roles with this permission

### Roles API (`/api/v1/roles`)

#### Create Role
- **POST** `/roles`
- **Body**: `{ name: string, description?: string, permissionIds?: string[] }`
- **Response**: Role with permissions and counts
- **Features**: Creates role and assigns custom permission IDs in transaction

#### List Roles
- **GET** `/roles?page=1&limit=10&search=term`
- **Response**: Paginated list with user/permission counts
- **Features**: Search by name/description, pagination

#### Get Role
- **GET** `/roles/:id`
- **Response**: Role details with full permission list

#### Update Role
- **PATCH** `/roles/:id`
- **Body**: `{ name?: string, description?: string, permissionIds?: string[] }`
- **Features**: Replaces all permissions if permissionIds provided

#### Delete Role
- **DELETE** `/roles/:id`
- **Features**: Prevents deletion if assigned to users

#### Assign Permissions
- **POST** `/roles/:id/permissions`
- **Body**: `{ permissionIds: string[] }`
- **Features**: Adds permissions, prevents duplicates

#### Remove Permissions
- **DELETE** `/roles/:id/permissions`
- **Body**: `{ permissionIds: string[] }`
- **Features**: Removes specified permissions

#### Get Role Users
- **GET** `/roles/:id/users?page=1&limit=10&search=term`
- **Response**: Paginated list of users with this role

### Role-Permissions API (`/api/v1/role-permissions`)

#### List All Relationships
- **GET** `/role-permissions?page=1&limit=10&search=term`
- **Response**: Paginated role-permission relationships
- **Features**: Search across roles and permissions

#### Bulk Assign
- **POST** `/role-permissions/bulk-assign`
- **Body**: `{ assignments: [{ roleId: string, permissionId: string }] }`
- **Response**: `{ created: number, skipped: number }`
- **Features**: Efficient bulk operations, skips existing

#### Bulk Remove
- **DELETE** `/role-permissions/bulk-remove`
- **Body**: `{ assignments: [{ roleId: string, permissionId: string }] }`
- **Response**: `{ removed: number, notFound: number }`

#### Copy Permissions
- **POST** `/role-permissions/copy-permissions`
- **Body**: `{ sourceRoleId: string, targetRoleId: string, replaceExisting?: boolean }`
- **Response**: `{ copied: number, skipped: number }`
- **Features**: Copy all permissions from one role to another

#### Get Analytics
- **GET** `/role-permissions/analytics`
- **Response**: Statistical data about role-permission relationships
- **Features**: Total counts, averages, distribution metrics

## Key Features

### 🔒 **Security & Validation**
- Comprehensive input validation using DTOs
- UUID validation for all IDs
- Unique constraint enforcement
- SQL injection prevention through Prisma

### 🚀 **Performance**
- Optimized database queries with parallel execution
- Efficient bulk operations
- Proper indexing on foreign keys
- Pagination to handle large datasets

### 🛡️ **Error Handling**
- Detailed error messages for different scenarios
- Database connection failure handling
- Conflict detection (duplicates, dependencies)
- Proper HTTP status codes

### 📊 **Data Integrity**
- Transactional operations for consistency
- Referential integrity enforcement
- Cascade deletion prevention for safety
- Audit trail with timestamps

### 📖 **Documentation**
- Complete Swagger/OpenAPI documentation
- Detailed endpoint descriptions
- Example requests and responses
- Error response documentation

### 🧪 **Testing**
- Unit tests for all services and controllers
- Proper mocking of dependencies
- Test coverage for error scenarios
- Integration test support

## Custom Permission ID Format

Permission IDs follow a hierarchical dot notation pattern:
- **Format**: `^[a-z][a-z0-9]*(\.[a-z][a-z0-9]*)*$`
- **Examples**:
  - `users.create` - Create users
  - `users.read` - View users
  - `users.update` - Update users
  - `users.delete` - Delete users
  - `reports.generate` - Generate reports
  - `admin.manage` - Administrative management
  - `content.publish` - Publish content

### Benefits of Custom Permission IDs:
1. **Human-readable** - Easy to understand what each permission does
2. **Hierarchical** - Logical grouping using dot notation
3. **Consistent** - Standardized naming convention
4. **Searchable** - Easy to filter and find permissions
5. **Integration-friendly** - Works well with frontend frameworks

## Usage Examples

### Creating Permissions with Custom IDs
```bash
POST /api/v1/permissions
{
  "id": "users.create",
  "name": "Create Users",
  "description": "Allows creating new user accounts"
}

POST /api/v1/permissions
{
  "id": "reports.generate",
  "name": "Generate Reports",
  "description": "Allows generating system reports"
}
```

### Creating a Role with Permissions
```bash
POST /api/v1/roles
{
  "name": "Content Manager",
  "description": "Can manage content and moderate users",
  "permissionIds": [
    "users.create",
    "users.read",
    "content.manage"
  ]
}
```

### Bulk Permission Assignment
```bash
POST /api/v1/role-permissions/bulk-assign
{
  "assignments": [
    { "roleId": "role-1-uuid", "permissionId": "users.create" },
    { "roleId": "role-1-uuid", "permissionId": "users.read" },
    { "roleId": "role-2-uuid", "permissionId": "reports.generate" }
  ]
}
```

### Copying Permissions Between Roles
```bash
POST /api/v1/role-permissions/copy-permissions
{
  "sourceRoleId": "admin-role-uuid",
  "targetRoleId": "manager-role-uuid",
  "replaceExisting": false
}
```

## Best Practices Implemented

1. **RESTful Design** - Consistent URL patterns and HTTP methods
2. **Pagination** - All list endpoints support pagination
3. **Search** - Flexible search across relevant fields
4. **Validation** - Comprehensive input validation
5. **Error Handling** - Detailed error responses
6. **Documentation** - Complete API documentation
7. **Testing** - Comprehensive test coverage
8. **Performance** - Optimized database queries
9. **Security** - Input sanitization and validation
10. **Maintainability** - Well-structured, commented code

## Next Steps

The API is ready for integration with:
- User authentication middleware
- Authorization guards using the permission system
- Frontend role management interfaces
- Audit logging for permission changes
- Advanced reporting and analytics

## 📊 **API Endpoints Summary**

### **Permissions API** (10 endpoints)
- `POST /permissions` - Create single permission
- `POST /permissions/bulk` - Create multiple permissions at once
- `GET /permissions` - List with search/pagination
- `GET /permissions/all` - **NEW**: Get all permissions without pagination
- `GET /permissions/:id` - Get specific permission
- `PATCH /permissions/:id` - Update permission
- `DELETE /permissions/:id` - Delete permission
- `GET /permissions/:id/roles` - Get roles with this permission

### **Roles API** (8 endpoints)
- `POST /roles` - Create role with optional permissions
- `GET /roles` - List with search/pagination
- `GET /roles/:id` - Get specific role with permissions
- `PATCH /roles/:id` - Update role and permissions
- `DELETE /roles/:id` - Delete role
- `POST /roles/:id/permissions` - Assign permissions
- `DELETE /roles/:id/permissions` - Remove permissions
- `GET /roles/:id/users` - Get users with this role

### **Role-Permissions API** (5 endpoints)
- `GET /role-permissions` - List all relationships
- `POST /role-permissions/bulk-assign` - Bulk assign operations
- `DELETE /role-permissions/bulk-remove` - Bulk remove operations
- `POST /role-permissions/copy-permissions` - Copy between roles
- `GET /role-permissions/analytics` - Statistical insights

**Total: 23 comprehensive API endpoints** for complete role-based access control management.
