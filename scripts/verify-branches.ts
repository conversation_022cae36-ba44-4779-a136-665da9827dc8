import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifyData() {
  console.log('🔍 Verifying seeded data...\n');

  try {
    // Get all branches with their regions
    const branches = await prisma.branch.findMany({
      include: {
        region: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    if (branches.length === 0) {
      console.log('❌ No branches found in the database');
      return;
    }

    console.log(`✅ Found ${branches.length} branch(es):\n`);

    branches.forEach((branch, index) => {
      console.log(`${index + 1}. Branch: ${branch.name}`);
      console.log(`   ID: ${branch.id}`);
      console.log(`   Region: ${branch.region.name}`);
      console.log(`   Region ID: ${branch.region_id}`);
      console.log('');
    });

    // Also show regions
    const regions = await prisma.region.findMany({
      include: {
        _count: {
          select: { branches: true },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    console.log(`📍 Available regions (${regions.length}):\n`);
    regions.forEach((region, index) => {
      console.log(`${index + 1}. Region: ${region.name}`);
      console.log(`   ID: ${region.id}`);
      console.log(`   Branch Count: ${region._count.branches}`);
      console.log('');
    });

    // Show customer categories
    const customerCategories = await prisma.customerCategory.findMany({
      orderBy: {
        name: 'asc',
      },
    });

    console.log(`🏷️ Customer Categories (${customerCategories.length}):\n`);
    customerCategories.forEach((category, index) => {
      console.log(`${index + 1}. Category: ${category.name}`);
      console.log(`   ID: ${category.id}`);
      console.log('');
    });

    // Show employers
    const employers = await prisma.employer.findMany({
      orderBy: {
        name: 'asc',
      },
    });

    console.log(`🏭 Employers (${employers.length}):\n`);
    employers.forEach((employer, index) => {
      console.log(`${index + 1}. Employer: ${employer.name}`);
      console.log(`   ID: ${employer.id}`);
      console.log('');
    });
  } catch (error) {
    console.error('❌ Error verifying data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyData();
