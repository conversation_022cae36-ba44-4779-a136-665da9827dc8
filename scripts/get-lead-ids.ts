import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function getLeadIds() {
  console.log('🔍 Getting recent leads...\n');

  try {
    const leads = await prisma.lead.findMany({
      orderBy: {
        id: 'desc',
      },
      take: 5,
      select: {
        id: true,
        customer_name: true,
        phone_number: true,
        parent_lead_id: true,
      },
    });

    console.log(`📋 Recent Leads (${leads.length}):\n`);
    leads.forEach((lead, index) => {
      console.log(`${index + 1}. Lead: ${lead.customer_name || 'Unknown'}`);
      console.log(`   ID: ${lead.id}`);
      console.log(`   Phone: ${lead.phone_number || 'N/A'}`);
      console.log(`   Parent Lead ID: ${lead.parent_lead_id || 'None'}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Error getting leads:', error);
  } finally {
    await prisma.$disconnect();
  }
}

getLeadIds();
