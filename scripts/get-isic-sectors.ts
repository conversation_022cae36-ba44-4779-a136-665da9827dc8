import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function getIsicSectors() {
  console.log('🔍 Getting ISIC sectors...\n');

  try {
    const sectors = await prisma.iSICSector.findMany({
      orderBy: {
        name: 'asc',
      },
    });

    console.log(`🏭 ISIC Sectors (${sectors.length}):\n`);
    sectors.forEach((sector, index) => {
      console.log(`${index + 1}. Sector: ${sector.name}`);
      console.log(`   ID: ${sector.id}`);
      console.log(`   Code: ${sector.code || 'N/A'}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Error getting ISIC sectors:', error);
  } finally {
    await prisma.$disconnect();
  }
}

getIsicSectors();
