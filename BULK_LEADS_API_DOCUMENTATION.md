# Bulk Leads Creation API Documentation

## Overview

The Bulk Leads Creation API allows you to create multiple leads at once with automatic foreign key handling. This is particularly useful for importing leads from Excel files or other bulk data sources.

## Endpoint

**POST** `/api/v1/leads/bulk`

## Features

- **Bulk Creation**: Create multiple leads in a single API call
- **Automatic Foreign Key Resolution**: For foreign key fields (branch, customer category, employer, ISIC sector), the system can:
  - Accept UUIDs for existing records
  - Accept names and automatically find existing records
  - Create new records if they don't exist
- **Error Handling**: Partial success - some leads can be created while others fail
- **Validation**: Each lead is validated individually

## Request Body

```json
{
  "leads": [
    {
      "customerName": "John Doe",
      "phoneNumber": "+************",
      "branchIdentifier": "Nairobi Branch",
      "customerCategoryIdentifier": "Individual",
      "employerIdentifier": "ABC Company",
      "isicSectorIdentifier": "Manufacturing",
      "leadType": "New",
      "leadStatus": "Pending",
      "contactPersonName": "<PERSON>",
      "contactPersonPhone": "+************",
      "clientId": "CLI-2024-001"
    },
    {
      "customerName": "<PERSON>",
      "phoneNumber": "+************",
      "branchIdentifier": "523b569f-30ae-46f4-bccb-f48c368b8a81",
      "customerCategoryIdentifier": "Corporate",
      "employerIdentifier": "XYZ Ltd",
      "isicSectorIdentifier": "f71002e5-1eef-4c53-9096-e421c0d40bad",
      "leadType": "Existing",
      "leadStatus": "Hot"
    }
  ]
}
```

## Field Descriptions

### Required Fields
- None (all fields are optional, but at least `customerName` is recommended)

### Optional Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `customerName` | string | Name of the customer | "John Doe" |
| `phoneNumber` | string | Customer's phone number | "+************" |
| `branchIdentifier` | string | Branch UUID or name | "Nairobi Branch" or UUID |
| `customerCategoryIdentifier` | string | Category UUID or name | "Individual" or UUID |
| `employerIdentifier` | string | Employer UUID or name | "ABC Company" or UUID |
| `isicSectorIdentifier` | string | ISIC Sector UUID or name | "Manufacturing" or UUID |
| `leadType` | string | Type of lead | "New" or "Existing" |
| `leadStatus` | string | Status of the lead | "Pending", "Hot", "Cold", etc. |
| `contactPersonName` | string | Contact person name | "Jane Smith" |
| `contactPersonPhone` | string | Contact person phone | "+************" |
| `clientId` | string | Unique client identifier | "CLI-2024-001" |
| `parentLeadId` | string | Parent lead UUID (for referrals) | UUID only |

## Foreign Key Handling

For the following fields, the system automatically handles both UUIDs and names:

1. **branchIdentifier**: 
   - If UUID: Validates existence
   - If name: Finds existing or creates new branch (requires default region)

2. **customerCategoryIdentifier**:
   - If UUID: Validates existence  
   - If name: Finds existing or creates new customer category

3. **employerIdentifier**:
   - If UUID: Validates existence
   - If name: Finds existing or creates new employer

4. **isicSectorIdentifier**:
   - If UUID: Validates existence
   - If name: Finds existing or creates new ISIC sector

## Response Format

```json
{
  "success": true,
  "message": "Successfully created 2 leads",
  "totalCreated": 2,
  "createdLeads": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "anchor_name": null,
      "lead_name": "John Doe",
      "status": "pending",
      "phoneNumber": "+************",
      "no_of_visits": 0,
      "no_of_calls": 0,
      "last_interaction": null,
      "customer_category": null,
      "isic_sector": null,
      "branch": null
    }
  ],
  "errors": []
}
```

## Error Handling

If some leads fail to create, the response will include an `errors` array:

```json
{
  "success": false,
  "message": "Successfully created 1 leads with 1 errors",
  "totalCreated": 1,
  "createdLeads": [...],
  "errors": [
    {
      "index": 1,
      "error": "Client ID 'CLI-2024-001' already exists",
      "leadData": {
        "customerName": "Mary Johnson",
        "clientId": "CLI-2024-001"
      }
    }
  ]
}
```

## Status Codes

- **201 Created**: Leads created successfully
- **400 Bad Request**: Invalid input data
- **409 Conflict**: Some leads could not be created due to conflicts

## Usage Examples

### Example 1: Basic Lead Creation
```bash
curl -X POST http://localhost:3000/api/v1/leads/bulk \
  -H "Content-Type: application/json" \
  -d '{
    "leads": [
      {
        "customerName": "Test Customer",
        "phoneNumber": "+254700000000",
        "leadType": "New"
      }
    ]
  }'
```

### Example 2: Excel Import Scenario
```bash
curl -X POST http://localhost:3000/api/v1/leads/bulk \
  -H "Content-Type: application/json" \
  -d '{
    "leads": [
      {
        "customerName": "Customer 1",
        "phoneNumber": "+254700000001",
        "branchIdentifier": "Nairobi Branch",
        "customerCategoryIdentifier": "Individual",
        "employerIdentifier": "Company A",
        "isicSectorIdentifier": "Manufacturing"
      },
      {
        "customerName": "Customer 2", 
        "phoneNumber": "+254700000002",
        "branchIdentifier": "Mombasa Branch",
        "customerCategoryIdentifier": "Corporate",
        "employerIdentifier": "Company B",
        "isicSectorIdentifier": "Services"
      }
    ]
  }'
```

## Performance Considerations

- Leads are processed in batches of 10 to avoid overwhelming the database
- Each lead creation is wrapped in error handling to ensure partial success
- Foreign key resolution is optimized with parallel queries where possible

## Integration with Frontend

This endpoint is designed to work seamlessly with Excel file imports in the frontend:

1. Parse Excel file data
2. Map columns to the appropriate field names
3. Send data to this endpoint
4. Display results showing successful creations and any errors
5. Allow users to fix errors and retry failed leads

## Notes

- Phone number validation accepts formats: '+254XXXXXXXXX', '07XXXXXXXX', '01XXXXXXXX'
- All string fields are trimmed and validated for length
- UUIDs are validated for proper format
- Case-insensitive matching is used for name-based lookups
