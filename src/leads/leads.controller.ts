import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Res,
  Header,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Express, Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
} from '@nestjs/swagger';
import { LeadsService } from './leads.service';
import { CreateLeadNewDto, BulkCreateLeadsDto, ExcelUploadResponseDto, ExcelUploadWithAnchorDto } from './dto/create-lead-new.dto';
import { UpdateLeadDto } from './dto/update-lead.dto';
import { LeadResponseDto } from './dto/lead-response.dto';
import { LeadSummaryResponseDto } from './dto/lead-summary-response.dto';
import { ConvertLeadDto } from './dto/convert-lead.dto';
import { ConvertLeadResponseDto } from './dto/convert-lead-response.dto';
import {
  CreateLeadContactPersonDto,
  UpdateLeadContactPersonDto,
  LeadContactPersonResponseDto,
} from './dto/lead-contact-person.dto';
import {
  PaginationDto,
  PaginatedResponseDto,
} from '../common/dto/pagination.dto';

/**
 * Controller handling all lead-related HTTP endpoints
 * Provides RESTful API for lead management
 */
@ApiTags('Leads')
@Controller('leads')
export class LeadsController {
  constructor(private readonly leadsService: LeadsService) {}

  /**
   * Creates a new lead and returns summary data
   * POST /leads
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new lead',
    description:
      'Creates a new lead with flexible input format and returns summary data including activity counts and last interaction.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Lead created successfully',
    type: LeadSummaryResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({
    description:
      'Referenced entity not found (customer category, ISIC sector, branch, or employer)',
  })
  @ApiConflictResponse({ description: 'Client ID already exists' })
  async create(
    @Body(ValidationPipe) createLeadNewDto: CreateLeadNewDto,
  ): Promise<LeadSummaryResponseDto> {
    return this.leadsService.createNewFormat(createLeadNewDto);
  }

  /**
   * Creates multiple leads at once with automatic foreign key handling
   * POST /leads/bulk
   */
  @Post('bulk')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create multiple leads at once',
    description:
      'Creates multiple leads in a single operation with automatic foreign key handling. For foreign key fields (branch, customer category, employer, ISIC sector), if the provided value is not a UUID, the system will attempt to find an existing record by name. If no record is found, a new one will be created automatically.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Leads created successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Successfully created 5 leads' },
        totalCreated: { type: 'number', example: 5 },
        createdLeads: {
          type: 'array',
          items: { $ref: '#/components/schemas/LeadSummaryResponseDto' },
        },
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              index: { type: 'number', example: 2 },
              error: { type: 'string', example: 'Client ID already exists' },
              leadData: { type: 'object' },
            },
          },
        },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiConflictResponse({ description: 'Some leads could not be created due to conflicts' })
  async createBulk(
    @Body(ValidationPipe) bulkCreateLeadsDto: BulkCreateLeadsDto,
  ) {
    return this.leadsService.createBulk(bulkCreateLeadsDto);
  }

  /**
   * Creates leads from an uploaded Excel file
   * POST /leads/upload-excel
   */
  @Post('upload-excel')
  @HttpCode(HttpStatus.CREATED)
  @UseInterceptors(FileInterceptor('file', {
    fileFilter: (req, file, callback) => {
      if (!file.originalname.match(/\.(xlsx|xls)$/)) {
        return callback(new BadRequestException('Only Excel files (.xlsx, .xls) are allowed'), false);
      }
      callback(null, true);
    },
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB limit
    },
  }))
  @ApiOperation({
    summary: 'Upload Excel file to create leads',
    description:
      'Uploads an Excel file containing lead data and creates leads in bulk. The system automatically parses the Excel file, maps columns to lead fields, and creates leads using the same logic as the bulk creation endpoint. Supports automatic foreign key resolution and provides detailed error reporting. Optionally accepts an anchorId to set as parent_lead_id for all imported leads.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Excel file processed successfully',
    type: ExcelUploadResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid file format, file too large, parsing errors, or invalid anchor ID'
  })
  @ApiConflictResponse({
    description: 'Some leads could not be created due to conflicts'
  })
  async uploadExcel(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: ExcelUploadWithAnchorDto,
  ): Promise<ExcelUploadResponseDto> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return this.leadsService.createFromExcel(file, body.anchorId);
  }

  /**
   * Retrieves all leads with pagination and search
   * GET /leads
   */
  @Get()
  @ApiOperation({
    summary: 'Get all leads',
    description:
      'Retrieves a paginated list of leads with optional search functionality. Search works on customer name, client ID, phone number, lead type, RM name, and branch name. Includes comprehensive relationship data.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10, max: 100)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description:
      'Search term for customer name, client ID, phone number, lead type, RM name, or branch name',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Leads retrieved successfully',
    type: PaginatedResponseDto<LeadSummaryResponseDto>,
  })
  async findAll(
    @Query(ValidationPipe) paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<LeadSummaryResponseDto>> {
    return this.leadsService.findAll(paginationDto);
  }

  /**
   * Retrieves all leads without pagination
   * GET /leads/all
   */
  @Get('all')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get all leads without pagination',
    description:
      'Retrieves all leads in the system without pagination. Useful for exports, reports, or when you need the complete dataset. Supports optional search filtering to narrow down results.',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Optional search term to filter leads by customer name, client ID, phone number, lead type, RM name, or branch name',
    example: 'John Doe',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'All leads retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/LeadSummaryResponseDto' },
        },
        total: { type: 'number', example: 1250 },
        message: { type: 'string', example: 'Retrieved all 1250 leads successfully' },
      },
    },
  })
  async findAllWithoutPagination(@Query('search') search?: string) {
    return this.leadsService.findAllWithoutPagination(search);
  }

  /**
   * Exports all leads to Excel file
   * GET /leads/export
   */
  @Get('export')
  @HttpCode(HttpStatus.OK)
  @Header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
  @ApiOperation({
    summary: 'Export all leads to Excel file',
    description:
      'Exports all leads in the system to an Excel file (.xlsx) and sends it as a downloadable file. The file includes comprehensive lead data with all relationships and activity counts. Supports optional search filtering to export specific leads.',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Optional search term to filter leads before export',
    example: 'corporate',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Excel file generated and sent successfully',
    headers: {
      'Content-Type': {
        description: 'MIME type for Excel files',
        schema: { type: 'string', example: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' },
      },
      'Content-Disposition': {
        description: 'Attachment header with filename',
        schema: { type: 'string', example: 'attachment; filename="leads-export-2025-07-30.xlsx"' },
      },
    },
  })
  async exportLeads(
    @Res() res: Response,
    @Query('search') search?: string,
  ): Promise<void> {
    const buffer = await this.leadsService.exportLeadsToExcel(search);

    // Generate filename with current date
    const date = new Date().toISOString().split('T')[0];
    const filename = `leads-export-${date}.xlsx`;

    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', buffer.length);

    // Send the Excel file
    res.end(buffer);
  }

  /**
   * Retrieves a single lead by ID
   * GET /leads/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get lead by ID',
    description:
      'Retrieves a single lead with comprehensive relationship data and activity counts. Includes all contact persons and related entity information.',
  })
  @ApiParam({ name: 'id', description: 'Lead UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lead retrieved successfully',
    type: LeadResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Lead not found' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<LeadResponseDto> {
    return this.leadsService.findOne(id);
  }

  /**
   * Updates a lead
   * PATCH /leads/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update lead',
    description:
      'Updates a lead with the provided information. All fields are optional for partial updates. Foreign key relationships are validated.',
  })
  @ApiParam({ name: 'id', description: 'Lead UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lead updated successfully',
    type: LeadSummaryResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({
    description: 'Lead not found or referenced entity not found',
  })
  @ApiConflictResponse({ description: 'Client ID already exists' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateLeadDto: UpdateLeadDto,
  ): Promise<LeadSummaryResponseDto> {
    return this.leadsService.update(id, updateLeadDto);
  }

  /**
   * Deletes a lead
   * DELETE /leads/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete lead',
    description:
      'Deletes a lead by ID. Cannot delete leads that have associated activities, hitlist entries, scheduled visits, loan activities, or referred leads. All contact persons are also deleted.',
  })
  @ApiParam({ name: 'id', description: 'Lead UUID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Lead deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Lead not found' })
  @ApiConflictResponse({
    description: 'Lead has associated data and cannot be deleted',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.leadsService.remove(id);
  }

  /**
   * Retrieves contact persons for a specific lead
   * GET /leads/:id/contact-persons
   */
  @Get(':id/contact-persons')
  @ApiOperation({
    summary: 'Get lead contact persons',
    description:
      'Retrieves all contact persons for a specific lead with pagination and search functionality.',
  })
  @ApiParam({ name: 'id', description: 'Lead UUID' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10, max: 100)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for contact person name or phone number',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Contact persons retrieved successfully',
    type: PaginatedResponseDto<LeadContactPersonResponseDto>,
  })
  @ApiNotFoundResponse({ description: 'Lead not found' })
  async getLeadContactPersons(
    @Param('id', ParseUUIDPipe) id: string,
    @Query(ValidationPipe) paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<LeadContactPersonResponseDto>> {
    return this.leadsService.getLeadContactPersons(id, paginationDto);
  }

  /**
   * Adds a contact person to a lead
   * POST /leads/contact-persons
   */
  @Post('contact-persons')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Add contact person to lead',
    description: 'Creates a new contact person for a specific lead.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Contact person created successfully',
    type: LeadContactPersonResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Lead not found' })
  async addContactPerson(
    @Body(ValidationPipe) createContactPersonDto: CreateLeadContactPersonDto,
  ): Promise<LeadContactPersonResponseDto> {
    return this.leadsService.addContactPerson(createContactPersonDto);
  }

  /**
   * Updates a contact person
   * PATCH /leads/contact-persons/:id
   */
  @Patch('contact-persons/:id')
  @ApiOperation({
    summary: 'Update contact person',
    description:
      'Updates a contact person with the provided information. All fields are optional for partial updates.',
  })
  @ApiParam({ name: 'id', description: 'Contact person UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Contact person updated successfully',
    type: LeadContactPersonResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Contact person not found' })
  async updateContactPerson(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateContactPersonDto: UpdateLeadContactPersonDto,
  ): Promise<LeadContactPersonResponseDto> {
    return this.leadsService.updateContactPerson(id, updateContactPersonDto);
  }

  /**
   * Deletes a contact person
   * DELETE /leads/contact-persons/:id
   */
  @Delete('contact-persons/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete contact person',
    description: 'Deletes a contact person by ID.',
  })
  @ApiParam({ name: 'id', description: 'Contact person UUID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Contact person deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Contact person not found' })
  async removeContactPerson(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<void> {
    return this.leadsService.removeContactPerson(id);
  }

  /**
   * Converts a lead by assigning an account number and generating a client ID
   * POST /leads/:id/convert
   */
  @Post(':id/convert')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Convert lead',
    description:
      'Converts a lead by assigning an account number and automatically generating a client ID in the format "CL865972". Returns the lead ID upon successful conversion.',
  })
  @ApiParam({ name: 'id', description: 'Lead UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lead converted successfully',
    type: ConvertLeadResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Lead not found' })
  @ApiConflictResponse({ description: 'Account number already in use' })
  async convertLead(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) convertLeadDto: ConvertLeadDto,
  ): Promise<ConvertLeadResponseDto> {
    return this.leadsService.convertLead(id, convertLeadDto);
  }
}
