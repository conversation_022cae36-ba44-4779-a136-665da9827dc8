import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Prisma } from '@prisma/client';
import {
  CreateLeadNewDto,
  BulkCreateLeadsDto,
  CreateLeadFlexibleDto,
  ExcelUploadResponseDto,
} from './dto/create-lead-new.dto';
import * as XLSX from 'xlsx';
import * as ExcelJS from 'exceljs';
import { Express } from 'express';
import { UpdateLeadDto } from './dto/update-lead.dto';
import {
  CreateLeadContactPersonDto,
  UpdateLeadContactPersonDto,
} from './dto/lead-contact-person.dto';
import { LeadSummaryResponseDto } from './dto/lead-summary-response.dto';
import { ConvertLeadDto } from './dto/convert-lead.dto';
import { ConvertLeadResponseDto } from './dto/convert-lead-response.dto';

import { PaginationDto } from '../common/dto/pagination.dto';

/**
 * Service handling all lead-related business logic
 * Provides CRUD operations and complex queries for leads
 */
@Injectable()
export class LeadsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new lead with the new format and returns summary data
   * Includes activity counts and last interaction information
   */
  async createNewFormat(
    createLeadNewDto: CreateLeadNewDto,
  ): Promise<LeadSummaryResponseDto> {
    const {
      branchId,
      clientId,
      contactPersonName,
      contactPersonPhone,
      createdDate,
      customerCategoryId,
      customerName,
      employerId,
      employerName,
      isicSectorId,
      leadType,
      leadStatus,
      phoneNumber,
      parentLeadId,
      anchorRelationshipId,
      anchorId,
    } = createLeadNewDto;

    // Convert empty strings to undefined
    const cleanBranchId =
      branchId && branchId.trim() !== '' ? branchId : undefined;
    const cleanClientId =
      clientId && clientId.trim() !== '' ? clientId : undefined;
    const cleanCustomerCategoryId =
      customerCategoryId && customerCategoryId.trim() !== ''
        ? customerCategoryId
        : undefined;
    const cleanEmployerId =
      employerId && employerId.trim() !== '' ? employerId : undefined;
    const cleanIsicSectorId =
      isicSectorId && isicSectorId.trim() !== '' ? isicSectorId : undefined;
    const cleanParentLeadId =
      parentLeadId && parentLeadId.trim() !== '' ? parentLeadId : undefined;

    // Handle anchor relationship ID and anchor ID mapping
    const cleanAnchorRelationshipId =
      anchorRelationshipId && anchorRelationshipId.trim() !== ''
        ? anchorRelationshipId
        : undefined;

    const cleanAnchorId =
      anchorId && anchorId.trim() !== '' ? anchorId : undefined;

    // Validate foreign key relationships in parallel (only if IDs are provided)
    const [
      customerCategory,
      isicSector,
      branch,
      employer,
      existingClientId,
      parentLead,
      anchorRelationship,
      anchorParentLead,
    ] = await Promise.all([
      cleanCustomerCategoryId
        ? this.prisma.customerCategory.findUnique({
            where: { id: cleanCustomerCategoryId },
          })
        : null,
      cleanIsicSectorId
        ? this.prisma.iSICSector.findUnique({
            where: { id: cleanIsicSectorId },
          })
        : null,
      cleanBranchId
        ? this.prisma.branch.findUnique({ where: { id: cleanBranchId } })
        : null,
      cleanEmployerId
        ? this.prisma.employer.findUnique({ where: { id: cleanEmployerId } })
        : null,
      cleanClientId
        ? this.prisma.lead.findUnique({ where: { client_id: cleanClientId } })
        : null,
      cleanParentLeadId
        ? this.prisma.lead.findUnique({ where: { id: cleanParentLeadId } })
        : null,
      cleanAnchorRelationshipId
        ? this.prisma.anchorRelationship.findUnique({
            where: { id: cleanAnchorRelationshipId },
          })
        : null,
      cleanAnchorId
        ? this.prisma.lead.findUnique({ where: { id: cleanAnchorId } })
        : null,
    ]);

    // Validate relationships only if IDs are provided
    if (cleanCustomerCategoryId && !customerCategory) {
      throw new NotFoundException(
        `Customer category with ID '${cleanCustomerCategoryId}' not found`,
      );
    }
    if (cleanIsicSectorId && !isicSector) {
      throw new NotFoundException(
        `ISIC sector with ID '${cleanIsicSectorId}' not found`,
      );
    }
    if (cleanBranchId && !branch) {
      throw new NotFoundException(
        `Branch with ID '${cleanBranchId}' not found`,
      );
    }
    if (cleanEmployerId && !employer) {
      throw new NotFoundException(
        `Employer with ID '${cleanEmployerId}' not found`,
      );
    }
    if (cleanParentLeadId && !parentLead) {
      throw new NotFoundException(
        `Parent lead with ID '${cleanParentLeadId}' not found`,
      );
    }
    if (cleanAnchorRelationshipId && !anchorRelationship) {
      throw new NotFoundException(
        `Anchor relationship with ID '${cleanAnchorRelationshipId}' not found`,
      );
    }
    if (cleanAnchorId && !anchorParentLead) {
      throw new NotFoundException(
        `Anchor lead with ID '${cleanAnchorId}' not found`,
      );
    }

    // Check for duplicate client ID
    if (cleanClientId && existingClientId) {
      throw new ConflictException(
        `Client ID '${cleanClientId}' already exists`,
      );
    }

    // Handle employer name - create employer if name is provided but no ID
    let finalEmployerId = cleanEmployerId;
    if (!cleanEmployerId && employerName && employerName.trim() !== '') {
      // Check if employer with this name already exists
      const existingEmployer = await this.prisma.employer.findFirst({
        where: {
          name: {
            equals: employerName.trim(),
            mode: 'insensitive',
          },
        },
      });

      if (existingEmployer) {
        finalEmployerId = existingEmployer.id;
      } else {
        // Create new employer
        const newEmployer = await this.prisma.employer.create({
          data: {
            name: employerName.trim(),
          },
        });
        finalEmployerId = newEmployer.id;
      }
    }

    // For now, we'll use a default RM user - in a real scenario, this would come from authentication
    // Let's get the first available user from the branch (if branch is provided)
    let rmUser;
    if (cleanBranchId) {
      rmUser = await this.prisma.user.findFirst({
        where: { branch_id: cleanBranchId },
      });
    } else {
      // If no branch is provided, get any user
      rmUser = await this.prisma.user.findFirst();
    }

    // Create lead with contact person in a transaction
    const lead = await this.prisma.$transaction(async (tx) => {
      const newLead = await tx.lead.create({
        data: {
          customer_name: customerName || 'Unknown Customer',
          customer_category_id: cleanCustomerCategoryId || undefined,
          isic_sector_id: cleanIsicSectorId || undefined,
          phone_number: phoneNumber || undefined,
          type_of_lead: leadType || 'New',
          lead_status: leadStatus || 'Pending',
          branch_id: cleanBranchId || undefined,
          rm_user_id: rmUser?.id || undefined,
          client_id: cleanClientId || undefined,
          employer_id: finalEmployerId || undefined,
          anchor_relationship_id: cleanAnchorRelationshipId || undefined,
          parent_lead_id: cleanAnchorId || cleanParentLeadId || undefined,
          contact_persons:
            contactPersonName && contactPersonPhone
              ? {
                  create: [
                    {
                      name: contactPersonName,
                      phone_number: contactPersonPhone,
                    },
                  ],
                }
              : undefined,
        },
      });

      return newLead;
    });

    // Fetch the created lead with all relationships to return proper summary data
    const createdLeadWithRelations = await this.prisma.lead.findUnique({
      where: { id: lead.id },
      include: this.getLeadIncludeOptions(),
    });

    // Return summary data using the same formatter as other methods
    return this.formatLeadSummaryResponse(createdLeadWithRelations);
  }

  /**
   * Creates multiple leads at once with automatic foreign key handling
   * For foreign key fields, if the value is not a UUID, attempts to find by name or creates new record
   */
  async createBulk(bulkCreateLeadsDto: BulkCreateLeadsDto) {
    const { leads } = bulkCreateLeadsDto;
    const createdLeads: LeadSummaryResponseDto[] = [];
    const errors: Array<{ index: number; error: string; leadData: any }> = [];

    // Process leads in batches to avoid overwhelming the database
    const batchSize = 10;
    for (let i = 0; i < leads.length; i += batchSize) {
      const batch = leads.slice(i, i + batchSize);

      // Process each lead in the current batch
      for (let j = 0; j < batch.length; j++) {
        const leadIndex = i + j;
        const leadData = batch[j];

        try {
          const createdLead =
            await this.createSingleLeadWithFlexibleKeys(leadData);
          createdLeads.push(createdLead);
        } catch (error) {
          errors.push({
            index: leadIndex,
            error: error.message || 'Unknown error occurred',
            leadData: leadData,
          });
        }
      }
    }

    return {
      success: errors.length === 0,
      message: `Successfully created ${createdLeads.length} leads${errors.length > 0 ? ` with ${errors.length} errors` : ''}`,
      totalCreated: createdLeads.length,
      createdLeads,
      errors,
    };
  }

  /**
   * Creates leads from an uploaded Excel file
   * Parses Excel file, maps columns to lead fields, and creates leads using bulk creation logic
   * @param file - The uploaded Excel file
   * @param anchorId - Optional parent lead ID to assign to all imported leads
   */
  async createFromExcel(
    file: Express.Multer.File,
    anchorId?: string,
  ): Promise<ExcelUploadResponseDto> {
    try {
      // Validate anchorId if provided
      if (anchorId) {
        const parentLead = await this.prisma.lead.findUnique({
          where: { id: anchorId },
        });
        if (!parentLead) {
          throw new BadRequestException(
            `Parent lead with ID '${anchorId}' not found`,
          );
        }
      }

      // Parse Excel file
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];

      if (!sheetName) {
        throw new BadRequestException('Excel file contains no sheets');
      }

      const worksheet = workbook.Sheets[sheetName];
      const rawData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
      }) as any[][];

      if (rawData.length === 0) {
        throw new BadRequestException('Excel file is empty');
      }

      // Extract headers and data rows
      const headers = rawData[0] as string[];
      const dataRows = rawData.slice(1) as any[][];

      if (dataRows.length === 0) {
        throw new BadRequestException('Excel file contains no data rows');
      }

      // Map Excel columns to lead fields
      const columnMappings = this.mapExcelColumns(headers);

      // Convert Excel rows to lead objects
      const { leads, parseErrors } = this.parseExcelRows(
        dataRows,
        headers,
        columnMappings,
        anchorId,
      );

      // Create leads using bulk creation logic
      const bulkResult = await this.createBulk({ leads });

      // Combine parsing errors with creation errors
      const allErrors = [
        ...parseErrors,
        ...bulkResult.errors.map((error) => ({
          row: error.index + 2, // +2 because index is 0-based and we skip header row
          error: error.error,
          data: error.leadData,
        })),
      ];

      return {
        success: allErrors.length === 0,
        message: `Successfully processed ${dataRows.length} rows from Excel file`,
        totalRows: rawData.length,
        processedRows: dataRows.length,
        successfulCreations: bulkResult.totalCreated,
        failedCreations: allErrors.length,
        createdLeads: bulkResult.createdLeads,
        errors: allErrors,
        columnMappings,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to process Excel file: ${error.message}`,
      );
    }
  }

  /**
   * Maps Excel column headers to lead field names
   * Uses exact and fuzzy matching to handle variations in column naming
   */
  private mapExcelColumns(headers: string[]): Record<string, string> {
    const mappings: Record<string, string> = {};

    // Define column mapping rules with priority (more specific patterns first)
    const columnRules = [
      {
        field: 'customerName',
        patterns: ['customer name', 'client name', 'lead name'],
      },
      {
        field: 'phoneNumber',
        patterns: ['phone number', 'mobile number', 'telephone'],
      },
      {
        field: 'branchIdentifier',
        patterns: ['branch', 'branch name', 'office', 'location'],
      },
      {
        field: 'customerCategoryIdentifier',
        patterns: ['customer category', 'category'],
      },
      {
        field: 'employerIdentifier',
        patterns: ['employer', 'company', 'organization', 'workplace'],
      },
      {
        field: 'isicSectorIdentifier',
        patterns: ['isic sector', 'sector', 'industry', 'business sector'],
      },
      { field: 'leadType', patterns: ['lead type', 'type of lead'] },
      { field: 'leadStatus', patterns: ['status', 'lead status', 'stage'] },
      {
        field: 'contactPersonName',
        patterns: ['contact person', 'contact name', 'representative'],
      },
      {
        field: 'contactPersonPhone',
        patterns: ['contact phone', 'contact number', 'representative phone'],
      },
      {
        field: 'clientId',
        patterns: ['client id', 'customer id', 'reference id'],
      },
      // Fallback patterns (less specific)
      { field: 'customerName', patterns: ['name'] },
      { field: 'phoneNumber', patterns: ['phone', 'mobile', 'contact'] },
      { field: 'customerCategoryIdentifier', patterns: ['type'] },
      { field: 'clientId', patterns: ['id'] },
    ];

    headers.forEach((header) => {
      const normalizedHeader = header.toLowerCase().trim();

      // Skip if already mapped
      if (mappings[header]) return;

      // Try exact matches first, then partial matches
      for (const rule of columnRules) {
        const exactMatch = rule.patterns.find(
          (pattern) => normalizedHeader === pattern,
        );
        if (exactMatch) {
          mappings[header] = rule.field;
          break;
        }
      }

      // If no exact match, try partial matches
      if (!mappings[header]) {
        for (const rule of columnRules) {
          const partialMatch = rule.patterns.find((pattern) =>
            normalizedHeader.includes(pattern),
          );
          if (partialMatch) {
            mappings[header] = rule.field;
            break;
          }
        }
      }
    });

    return mappings;
  }

  /**
   * Parses Excel data rows into lead objects
   * Handles data validation and type conversion
   * @param dataRows - Array of Excel data rows
   * @param headers - Array of Excel column headers
   * @param columnMappings - Mapping of Excel columns to lead fields
   * @param anchorId - Optional parent lead ID to assign to all leads
   */
  private parseExcelRows(
    dataRows: any[][],
    headers: string[],
    columnMappings: Record<string, string>,
    anchorId?: string,
  ): {
    leads: CreateLeadFlexibleDto[];
    parseErrors: Array<{ row: number; error: string; data: any }>;
  } {
    const leads: CreateLeadFlexibleDto[] = [];
    const parseErrors: Array<{ row: number; error: string; data: any }> = [];

    dataRows.forEach((row, rowIndex) => {
      try {
        const leadData: CreateLeadFlexibleDto = {};
        const rowData: Record<string, any> = {};

        // Map row data to object using headers
        headers.forEach((header, colIndex) => {
          const value = row[colIndex];
          rowData[header] = value;

          const fieldName = columnMappings[header];
          if (
            fieldName &&
            value !== undefined &&
            value !== null &&
            value !== ''
          ) {
            // Clean and convert the value
            const cleanValue = String(value).trim();
            if (cleanValue) {
              (leadData as any)[fieldName] = cleanValue;
            }
          }
        });

        // Validate required fields or set defaults
        if (!leadData.customerName) {
          // Try to find customer name in unmapped columns
          const possibleNameColumns = headers.filter(
            (h) => h.toLowerCase().includes('name') && !columnMappings[h],
          );
          if (possibleNameColumns.length > 0) {
            const nameValue = rowData[possibleNameColumns[0]];
            if (nameValue) {
              leadData.customerName = String(nameValue).trim();
            }
          }
        }

        // Set default values if not provided
        if (!leadData.leadType) {
          leadData.leadType = 'New';
        }
        if (!leadData.leadStatus) {
          leadData.leadStatus = 'Pending';
        }

        // Validate phone number format if provided
        if (leadData.phoneNumber) {
          leadData.phoneNumber = this.normalizePhoneNumber(
            leadData.phoneNumber,
          );
        }

        // Assign anchorId as parent_lead_id if provided
        if (anchorId) {
          leadData.parentLeadId = anchorId;
        }

        leads.push(leadData);
      } catch (error) {
        parseErrors.push({
          row: rowIndex + 2, // +2 because rowIndex is 0-based and we skip header row
          error: `Row parsing error: ${error.message}`,
          data: row,
        });
      }
    });

    return { leads, parseErrors };
  }

  /**
   * Normalizes phone number format
   * Handles various input formats and converts to standard format
   */
  private normalizePhoneNumber(phone: string): string {
    const cleaned = phone.replace(/\s+/g, '').replace(/[^\d+]/g, '');

    // Handle different formats
    if (cleaned.startsWith('+254')) {
      return cleaned;
    } else if (cleaned.startsWith('254')) {
      return '+' + cleaned;
    } else if (cleaned.startsWith('07') || cleaned.startsWith('01')) {
      return '+254' + cleaned.substring(1);
    } else if (
      cleaned.length === 9 &&
      (cleaned.startsWith('7') || cleaned.startsWith('1'))
    ) {
      return '+254' + cleaned;
    }

    return phone; // Return original if no pattern matches
  }

  /**
   * Helper method to create a single lead with flexible foreign key handling
   * Resolves foreign keys by UUID or name, creating new records if needed
   */
  private async createSingleLeadWithFlexibleKeys(
    leadData: CreateLeadFlexibleDto,
  ): Promise<LeadSummaryResponseDto> {
    const {
      branchIdentifier,
      clientId,
      contactPersonName,
      contactPersonPhone,
      createdDate,
      customerCategoryIdentifier,
      customerName,
      employerIdentifier,
      isicSectorIdentifier,
      leadType,
      phoneNumber,
      parentLeadId,
      leadStatus,
    } = leadData;

    // Clean up empty strings
    const cleanClientId =
      clientId && clientId.trim() !== '' ? clientId : undefined;
    const cleanParentLeadId =
      parentLeadId && parentLeadId.trim() !== '' ? parentLeadId : undefined;

    // Resolve foreign key relationships
    const [
      branchId,
      customerCategoryId,
      employerId,
      isicSectorId,
      existingClientId,
      parentLead,
    ] = await Promise.all([
      this.resolveBranchId(branchIdentifier),
      this.resolveCustomerCategoryId(customerCategoryIdentifier),
      this.resolveEmployerId(employerIdentifier),
      this.resolveIsicSectorId(isicSectorIdentifier),
      cleanClientId
        ? this.prisma.lead.findUnique({ where: { client_id: cleanClientId } })
        : null,
      cleanParentLeadId
        ? this.prisma.lead.findUnique({ where: { id: cleanParentLeadId } })
        : null,
    ]);

    // Validate parent lead exists if provided
    if (cleanParentLeadId && !parentLead) {
      throw new NotFoundException(
        `Parent lead with ID '${cleanParentLeadId}' not found`,
      );
    }

    // Check for duplicate client ID
    if (cleanClientId && existingClientId) {
      throw new ConflictException(
        `Client ID '${cleanClientId}' already exists`,
      );
    }

    // Get RM user (same logic as original method)
    let rmUser;
    if (branchId) {
      rmUser = await this.prisma.user.findFirst({
        where: { branch_id: branchId },
      });
    } else {
      rmUser = await this.prisma.user.findFirst();
    }

    // Create lead with contact person in a transaction
    const lead = await this.prisma.$transaction(async (tx) => {
      const newLead = await tx.lead.create({
        data: {
          customer_name: customerName || 'Unknown Customer',
          customer_category_id: customerCategoryId || undefined,
          isic_sector_id: isicSectorId || undefined,
          phone_number: phoneNumber || undefined,
          type_of_lead: leadType || 'New',
          lead_status: leadStatus || 'Pending',
          branch_id: branchId || undefined,
          rm_user_id: rmUser?.id || undefined,
          client_id: cleanClientId || undefined,
          employer_id: employerId || undefined,
          parent_lead_id: cleanParentLeadId || undefined,
          contact_persons:
            contactPersonName && contactPersonPhone
              ? {
                  create: [
                    {
                      name: contactPersonName,
                      phone_number: contactPersonPhone,
                    },
                  ],
                }
              : undefined,
        },
      });

      return newLead;
    });

    // Return summary data
    return {
      id: lead.id,
      parent_lead_id: null,
      anchor_relationship_id: null,
      lead_name: customerName || 'Unknown Customer',
      status: leadStatus || 'Pending', // Return actual lead_status from database, default to 'Pending'
      phoneNumber: phoneNumber || '',
      no_of_visits: 0,
      no_of_calls: 0,
      last_interaction: null,
      officer: null,
      customer_category: null,
      isic_sector: null,
      branch: null,
      employerName: null,
      parent_lead_name: null,
      anchor_relationship_name: null, // New leads don't have parent lead data loaded
    };
  }

  /**
   * Retrieves all leads with pagination, search, and filtering
   * Optimized with selective field inclusion and parallel queries
   */
  async findAll(paginationDto: PaginationDto) {
    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    // Build dynamic where clause for search functionality
    const whereClause: Prisma.LeadWhereInput = search
      ? {
          OR: [
            {
              customer_name: {
                contains: search,
                mode: 'insensitive',
              },
            },
            {
              client_id: {
                contains: search,
                mode: 'insensitive',
              },
            },
            {
              phone_number: {
                contains: search,
                mode: 'insensitive',
              },
            },
            {
              type_of_lead: {
                contains: search,
                mode: 'insensitive',
              },
            },
            {
              rm_user: {
                name: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
            },
            {
              branch: {
                name: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
            },
          ],
        }
      : {};

    // Execute queries in parallel for better performance
    const [leads, total] = await Promise.all([
      this.prisma.lead.findMany({
        where: whereClause,
        skip,
        take: limit,
        include: this.getLeadIncludeOptions(),
        orderBy: {
          customer_name: 'asc' as const,
        },
      }),
      this.prisma.lead.count({
        where: whereClause,
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    // Format leads with activity counts and summary data
    const formattedLeads = await Promise.all(
      leads.map(async (lead) => await this.formatLeadSummaryResponse(lead)),
    );

    return {
      data: formattedLeads,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Retrieves all leads without pagination
   * Useful for exports, reports, or when you need the complete dataset
   * @param search - Optional search term to filter leads
   */
  async findAllWithoutPagination(search?: string) {
    // Build search conditions (same as paginated version)
    const whereClause = search
      ? {
          OR: [
            {
              customer_name: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
            {
              client_id: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
            {
              phone_number: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
            {
              type_of_lead: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
            {
              rm_user: {
                name: {
                  contains: search,
                  mode: 'insensitive' as const,
                },
              },
            },
            {
              branch: {
                name: {
                  contains: search,
                  mode: 'insensitive' as const,
                },
              },
            },
          ],
        }
      : {};

    // Execute queries in parallel for better performance
    const [leads, total] = await Promise.all([
      this.prisma.lead.findMany({
        where: whereClause,
        include: this.getLeadIncludeOptions(),
        orderBy: {
          customer_name: 'asc' as const,
        },
      }),
      this.prisma.lead.count({
        where: whereClause,
      }),
    ]);

    // Format leads with activity counts and summary data
    const formattedLeads = await Promise.all(
      leads.map(async (lead) => await this.formatLeadSummaryResponse(lead)),
    );

    return {
      data: formattedLeads,
      total,
      message: `Retrieved all ${total} leads successfully${search ? ` matching "${search}"` : ''}`,
    };
  }

  /**
   * Exports all leads to Excel file
   * Creates a comprehensive Excel file with all lead data and relationships
   * @param search - Optional search term to filter leads
   * @returns Buffer containing the Excel file
   */
  async exportLeadsToExcel(search?: string): Promise<Buffer> {
    // Get all leads data (same as findAllWithoutPagination but with more detailed data)
    const whereClause = search
      ? {
          OR: [
            {
              customer_name: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
            {
              client_id: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
            {
              phone_number: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
            {
              type_of_lead: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
            {
              rm_user: {
                name: {
                  contains: search,
                  mode: 'insensitive' as const,
                },
              },
            },
            {
              branch: {
                name: {
                  contains: search,
                  mode: 'insensitive' as const,
                },
              },
            },
          ],
        }
      : {};

    // Fetch leads with comprehensive data
    const leads = await this.prisma.lead.findMany({
      where: whereClause,
      include: {
        ...this.getLeadIncludeOptions(),
        activities: {
          orderBy: {
            created_at: 'desc',
          },
        },
      },
      orderBy: {
        customer_name: 'asc',
      },
    });

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Leads Export');

    // Define columns with proper headers
    worksheet.columns = [
      { header: 'Lead Name', key: 'customerName', width: 25 },
      { header: 'Phone Number', key: 'phoneNumber', width: 20 },
      { header: 'Client ID', key: 'clientId', width: 15 },
      { header: 'Lead Type', key: 'leadType', width: 15 },
      { header: 'Lead Status', key: 'leadStatus', width: 15 },
      { header: 'Parent Lead', key: 'parentLead', width: 25 },
      { header: 'Branch', key: 'branch', width: 20 },
      { header: 'Region', key: 'region', width: 20 },
      { header: 'Customer Category', key: 'customerCategory', width: 20 },
      { header: 'ISIC Sector', key: 'isicSector', width: 25 },
      { header: 'Employer', key: 'employer', width: 25 },
      { header: 'RM User', key: 'rmUser', width: 25 },
      { header: 'Contact Persons', key: 'contactPersons', width: 30 },
      { header: 'No. of Visits', key: 'visitCount', width: 15 },
      { header: 'No. of Calls', key: 'callCount', width: 15 },
      { header: 'Last Interaction', key: 'lastInteraction', width: 20 },
      { header: 'Created Date', key: 'createdDate', width: 20 },
    ];

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '366092' },
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };

    // Add data rows
    leads.forEach((lead: any) => {
      const contactPersons =
        lead.contact_persons
          ?.map((cp: any) => `${cp.name} (${cp.phone_number || 'No phone'})`)
          .join('; ') || '';

      const lastActivity = lead.activities?.[0];
      const lastInteraction = lastActivity
        ? lastActivity.created_at?.toISOString().split('T')[0]
        : null;

      // Count visits and calls from activities
      const visitCount =
        lead.activities?.filter((a: any) => a.activity_type === 'visit')
          .length || 0;
      const callCount =
        lead.activities?.filter((a: any) => a.activity_type === 'call')
          .length || 0;

      worksheet.addRow({
        customerName: lead.customer_name || 'Unknown Customer',
        phoneNumber: lead.phone_number || '',
        clientId: lead.client_id || '',
        leadType: lead.type_of_lead || '',
        leadStatus: lead.lead_status || '',
        parentLead: lead.leads?.customer_name || '',
        branch: lead.branch?.name || '',
        region: lead.branch?.region?.name || '',
        customerCategory: lead.customer_category?.name || '',
        isicSector: lead.isic_sector?.name || '',
        employer: lead.employer?.name || '',
        rmUser: lead.rm_user?.name || '',
        contactPersons: contactPersons,
        visitCount: visitCount,
        callCount: callCount,
        lastInteraction: lastInteraction,
        createdDate: '', // Lead model doesn't have created_at field
      });
    });

    // Auto-fit columns and add borders
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        // Alternate row colors for better readability
        if (rowNumber > 1 && rowNumber % 2 === 0) {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F8F9FA' },
          };
        }
      });
    });

    // Add summary information at the bottom
    const summaryStartRow = leads.length + 3;
    worksheet.getCell(`A${summaryStartRow}`).value = 'Export Summary:';
    worksheet.getCell(`A${summaryStartRow}`).font = { bold: true };
    worksheet.getCell(`A${summaryStartRow + 1}`).value =
      `Total Leads: ${leads.length}`;
    worksheet.getCell(`A${summaryStartRow + 2}`).value =
      `Export Date: ${new Date().toISOString().split('T')[0]}`;
    worksheet.getCell(`A${summaryStartRow + 3}`).value =
      `Search Filter: ${search || 'None'}`;

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  /**
   * Retrieves a single lead by ID with all related data
   * Includes comprehensive relationship data and activity counts
   */
  async findOne(id: string) {
    const lead = await this.prisma.lead.findUnique({
      where: { id },
      include: {
        ...this.getLeadIncludeOptions(),
        // Include counts for additional insights
        _count: {
          select: {
            other_leads: true,
            general_activities: true,
            hitlist_entries: true,
            scheduled_visits: true,
            loan_activities: true,
          },
        },
      },
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID '${id}' not found`);
    }

    return this.formatLeadResponse(lead, true);
  }

  /**
   * Updates a lead with validation of foreign key relationships
   * Handles partial updates and maintains data integrity
   * Returns summary data format consistent with create and list endpoints
   */
  async update(
    id: string,
    updateLeadDto: UpdateLeadDto,
  ): Promise<LeadSummaryResponseDto> {
    const existingLead = await this.prisma.lead.findUnique({
      where: { id },
    });

    if (!existingLead) {
      throw new NotFoundException(`Lead with ID '${id}' not found`);
    }

    const {
      branchId,
      clientId,
      contactPersonName,
      contactPersonPhone,
      createdDate,
      customerCategoryId,
      customerName,
      employerId,
      employerName,
      isicSectorId,
      leadType,
      leadStatus,
      phoneNumber,
      parentLeadId,
      anchorRelationshipId,
      anchorId,
    } = updateLeadDto;

    // Convert empty strings to undefined
    const cleanBranchId =
      branchId && branchId.trim() !== '' ? branchId : undefined;
    const cleanClientId =
      clientId && clientId.trim() !== '' ? clientId : undefined;
    const cleanCustomerCategoryId =
      customerCategoryId && customerCategoryId.trim() !== ''
        ? customerCategoryId
        : undefined;
    const cleanEmployerId =
      employerId && employerId.trim() !== '' ? employerId : undefined;
    const cleanIsicSectorId =
      isicSectorId && isicSectorId.trim() !== '' ? isicSectorId : undefined;
    const cleanParentLeadId =
      parentLeadId && parentLeadId.trim() !== '' ? parentLeadId : undefined;

    // Handle anchor relationship ID and anchor ID mapping
    const cleanAnchorRelationshipId =
      anchorRelationshipId && anchorRelationshipId.trim() !== ''
        ? anchorRelationshipId
        : undefined;

    const cleanAnchorId =
      anchorId && anchorId.trim() !== '' ? anchorId : undefined;

    // Validate foreign key relationships in parallel (only if IDs are provided)
    const [
      customerCategory,
      isicSector,
      branch,
      employer,
      existingClientId,
      parentLead,
      anchorRelationship,
      anchorParentLead,
    ] = await Promise.all([
      cleanCustomerCategoryId
        ? this.prisma.customerCategory.findUnique({
            where: { id: cleanCustomerCategoryId },
          })
        : null,
      cleanIsicSectorId
        ? this.prisma.iSICSector.findUnique({
            where: { id: cleanIsicSectorId },
          })
        : null,
      cleanBranchId
        ? this.prisma.branch.findUnique({ where: { id: cleanBranchId } })
        : null,
      cleanEmployerId
        ? this.prisma.employer.findUnique({ where: { id: cleanEmployerId } })
        : null,
      cleanClientId && cleanClientId !== existingLead.client_id
        ? this.prisma.lead.findUnique({ where: { client_id: cleanClientId } })
        : null,
      cleanParentLeadId
        ? this.prisma.lead.findUnique({ where: { id: cleanParentLeadId } })
        : null,
      cleanAnchorRelationshipId
        ? this.prisma.anchorRelationship.findUnique({
            where: { id: cleanAnchorRelationshipId },
          })
        : null,
      cleanAnchorId
        ? this.prisma.lead.findUnique({ where: { id: cleanAnchorId } })
        : null,
    ]);

    // Validate relationships only if IDs are provided
    if (cleanCustomerCategoryId && !customerCategory) {
      throw new NotFoundException(
        `Customer category with ID '${cleanCustomerCategoryId}' not found`,
      );
    }
    if (cleanIsicSectorId && !isicSector) {
      throw new NotFoundException(
        `ISIC sector with ID '${cleanIsicSectorId}' not found`,
      );
    }
    if (cleanBranchId && !branch) {
      throw new NotFoundException(
        `Branch with ID '${cleanBranchId}' not found`,
      );
    }
    if (cleanEmployerId && !employer) {
      throw new NotFoundException(
        `Employer with ID '${cleanEmployerId}' not found`,
      );
    }
    if (cleanParentLeadId && !parentLead) {
      throw new NotFoundException(
        `Parent lead with ID '${cleanParentLeadId}' not found`,
      );
    }
    if (cleanAnchorRelationshipId && !anchorRelationship) {
      throw new NotFoundException(
        `Anchor relationship with ID '${cleanAnchorRelationshipId}' not found`,
      );
    }
    if (cleanAnchorId && !anchorParentLead) {
      throw new NotFoundException(
        `Anchor lead with ID '${cleanAnchorId}' not found`,
      );
    }

    // Check for duplicate client ID
    if (cleanClientId && existingClientId) {
      throw new ConflictException(
        `Client ID '${cleanClientId}' already exists`,
      );
    }

    // Handle employer name - create employer if name is provided but no ID
    let finalEmployerId = cleanEmployerId;
    if (!cleanEmployerId && employerName && employerName.trim() !== '') {
      // Check if employer with this name already exists
      const existingEmployer = await this.prisma.employer.findFirst({
        where: {
          name: {
            equals: employerName.trim(),
            mode: 'insensitive',
          },
        },
      });

      if (existingEmployer) {
        finalEmployerId = existingEmployer.id;
      } else {
        // Create new employer
        const newEmployer = await this.prisma.employer.create({
          data: {
            name: employerName.trim(),
          },
        });
        finalEmployerId = newEmployer.id;
      }
    }

    // Prepare update data - only include fields that are provided
    const updateData: any = {};

    if (customerName !== undefined) updateData.customer_name = customerName;
    if (cleanCustomerCategoryId !== undefined)
      updateData.customer_category_id = cleanCustomerCategoryId;
    if (cleanIsicSectorId !== undefined)
      updateData.isic_sector_id = cleanIsicSectorId;
    if (phoneNumber !== undefined) updateData.phone_number = phoneNumber;

    // leadType maps to type_of_lead field (existing field)
    if (leadType !== undefined) updateData.type_of_lead = leadType;

    // leadStatus maps to lead_status field (new field)
    if (leadStatus !== undefined) updateData.lead_status = leadStatus;

    if (cleanBranchId !== undefined) updateData.branch_id = cleanBranchId;
    if (cleanClientId !== undefined) updateData.client_id = cleanClientId;
    if (finalEmployerId !== undefined) updateData.employer_id = finalEmployerId;
    if (cleanAnchorRelationshipId !== undefined)
      updateData.anchor_relationship_id = cleanAnchorRelationshipId;
    if (cleanAnchorId !== undefined || cleanParentLeadId !== undefined)
      updateData.parent_lead_id = cleanAnchorId || cleanParentLeadId;

    // Update the lead
    await this.prisma.lead.update({
      where: { id },
      data: updateData,
    });

    // Fetch the updated lead with all related data
    const updatedLead = await this.prisma.lead.findUnique({
      where: { id },
      include: this.getLeadIncludeOptions(),
    });

    // Handle contact person updates if provided
    if (contactPersonName !== undefined || contactPersonPhone !== undefined) {
      // For simplicity, we'll update the first contact person or create one if none exists
      const existingContactPerson =
        await this.prisma.leadContactPerson.findFirst({
          where: { lead_id: id },
        });

      if (existingContactPerson) {
        // Update existing contact person
        const contactUpdateData: any = {};
        if (contactPersonName !== undefined)
          contactUpdateData.name = contactPersonName;
        if (contactPersonPhone !== undefined)
          contactUpdateData.phone_number = contactPersonPhone;

        if (Object.keys(contactUpdateData).length > 0) {
          await this.prisma.leadContactPerson.update({
            where: { id: existingContactPerson.id },
            data: contactUpdateData,
          });
        }
      } else if (contactPersonName && contactPersonPhone) {
        // Create new contact person if both name and phone are provided
        await this.prisma.leadContactPerson.create({
          data: {
            lead_id: id,
            name: contactPersonName,
            phone_number: contactPersonPhone,
          },
        });
      }
    }

    // Fetch the updated lead with all relationships to return proper summary data
    const updatedLeadWithRelations = await this.prisma.lead.findUnique({
      where: { id },
      include: this.getLeadIncludeOptions(),
    });

    // Return summary data format
    return this.formatLeadSummaryResponse(updatedLeadWithRelations);
  }

  /**
   * Deletes a lead and all associated contact persons
   * Checks for dependencies before deletion
   */
  async remove(id: string): Promise<void> {
    const lead = await this.prisma.lead.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            other_leads: true,
            general_activities: true,
            hitlist_entries: true,
            scheduled_visits: true,
            loan_activities: true,
          },
        },
      },
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID '${id}' not found`);
    }

    // Check if lead has dependencies that prevent deletion
    const hasActivities = lead._count.general_activities > 0;
    const hasHitlistEntries = lead._count.hitlist_entries > 0;
    const hasScheduledVisits = lead._count.scheduled_visits > 0;
    const hasLoanActivities = lead._count.loan_activities > 0;
    const hasReferredLeads = lead._count.other_leads > 0;

    if (
      hasActivities ||
      hasHitlistEntries ||
      hasScheduledVisits ||
      hasLoanActivities ||
      hasReferredLeads
    ) {
      throw new ConflictException(
        'Lead cannot be deleted because it has associated activities, hitlist entries, scheduled visits, loan activities, or referred leads',
      );
    }

    // Delete lead and associated contact persons in a transaction
    await this.prisma.$transaction(async (tx) => {
      // Delete contact persons first
      await tx.leadContactPerson.deleteMany({
        where: { lead_id: id },
      });

      // Delete the lead
      await tx.lead.delete({
        where: { id },
      });
    });
  }

  /**
   * Adds a contact person to a lead
   */
  async addContactPerson(createContactPersonDto: CreateLeadContactPersonDto) {
    const { lead_id, name, phone_number } = createContactPersonDto;

    // Verify lead exists
    const lead = await this.prisma.lead.findUnique({
      where: { id: lead_id },
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID '${lead_id}' not found`);
    }

    const contactPerson = await this.prisma.leadContactPerson.create({
      data: {
        lead_id,
        name,
        phone_number,
      },
    });

    return contactPerson;
  }

  /**
   * Updates a contact person
   */
  async updateContactPerson(
    id: string,
    updateContactPersonDto: UpdateLeadContactPersonDto,
  ) {
    const existingContactPerson =
      await this.prisma.leadContactPerson.findUnique({
        where: { id },
      });

    if (!existingContactPerson) {
      throw new NotFoundException(`Contact person with ID '${id}' not found`);
    }

    const updatedContactPerson = await this.prisma.leadContactPerson.update({
      where: { id },
      data: updateContactPersonDto,
    });

    return updatedContactPerson;
  }

  /**
   * Deletes a contact person
   */
  async removeContactPerson(id: string): Promise<void> {
    const contactPerson = await this.prisma.leadContactPerson.findUnique({
      where: { id },
    });

    if (!contactPerson) {
      throw new NotFoundException(`Contact person with ID '${id}' not found`);
    }

    await this.prisma.leadContactPerson.delete({
      where: { id },
    });
  }

  /**
   * Gets contact persons for a specific lead
   */
  async getLeadContactPersons(leadId: string, paginationDto: PaginationDto) {
    // Verify lead exists
    const lead = await this.prisma.lead.findUnique({
      where: { id: leadId },
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID '${leadId}' not found`);
    }

    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    const whereClause: Prisma.LeadContactPersonWhereInput = {
      lead_id: leadId,
      ...(search && {
        OR: [
          {
            name: {
              contains: search,
              mode: 'insensitive',
            },
          },
          {
            phone_number: {
              contains: search,
              mode: 'insensitive',
            },
          },
        ],
      }),
    };

    const [contactPersons, total] = await Promise.all([
      this.prisma.leadContactPerson.findMany({
        where: whereClause,
        skip,
        take: limit,
        orderBy: {
          name: 'asc' as const,
        },
      }),
      this.prisma.leadContactPerson.count({
        where: whereClause,
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: contactPersons,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Helper method to resolve branch ID from identifier (UUID or name)
   * Creates new branch if name is provided and doesn't exist
   */
  private async resolveBranchId(
    identifier?: string,
  ): Promise<string | undefined> {
    if (!identifier || identifier.trim() === '') {
      return undefined;
    }

    const cleanIdentifier = identifier.trim();

    // Check if it's a UUID
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanIdentifier)) {
      // Validate UUID exists
      const branch = await this.prisma.branch.findUnique({
        where: { id: cleanIdentifier },
      });
      if (!branch) {
        throw new NotFoundException(
          `Branch with ID '${cleanIdentifier}' not found`,
        );
      }
      return cleanIdentifier;
    }

    // Try to find by name
    let branch = await this.prisma.branch.findFirst({
      where: {
        name: {
          equals: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    // If not found, create new branch (requires a default region)
    if (!branch) {
      // Get the first available region or create a default one
      let defaultRegion = await this.prisma.region.findFirst();
      if (!defaultRegion) {
        defaultRegion = await this.prisma.region.create({
          data: { name: 'Default Region' },
        });
      }

      branch = await this.prisma.branch.create({
        data: {
          name: cleanIdentifier,
          region_id: defaultRegion.id,
        },
      });
    }

    return branch.id;
  }

  /**
   * Helper method to resolve customer category ID from identifier (UUID or name)
   * Creates new customer category if name is provided and doesn't exist
   */
  private async resolveCustomerCategoryId(
    identifier?: string,
  ): Promise<string | undefined> {
    if (!identifier || identifier.trim() === '') {
      return undefined;
    }

    const cleanIdentifier = identifier.trim();

    // Check if it's a UUID
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanIdentifier)) {
      // Validate UUID exists
      const category = await this.prisma.customerCategory.findUnique({
        where: { id: cleanIdentifier },
      });
      if (!category) {
        throw new NotFoundException(
          `Customer category with ID '${cleanIdentifier}' not found`,
        );
      }
      return cleanIdentifier;
    }

    // Try to find by name
    let category = await this.prisma.customerCategory.findFirst({
      where: {
        name: {
          equals: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    // If not found, create new category
    if (!category) {
      category = await this.prisma.customerCategory.create({
        data: { name: cleanIdentifier },
      });
    }

    return category.id;
  }

  /**
   * Helper method to resolve employer ID from identifier (UUID or name)
   * Creates new employer if name is provided and doesn't exist
   */
  private async resolveEmployerId(
    identifier?: string,
  ): Promise<string | undefined> {
    if (!identifier || identifier.trim() === '') {
      return undefined;
    }

    const cleanIdentifier = identifier.trim();

    // Check if it's a UUID
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanIdentifier)) {
      // Validate UUID exists
      const employer = await this.prisma.employer.findUnique({
        where: { id: cleanIdentifier },
      });
      if (!employer) {
        throw new NotFoundException(
          `Employer with ID '${cleanIdentifier}' not found`,
        );
      }
      return cleanIdentifier;
    }

    // Try to find by name
    let employer = await this.prisma.employer.findFirst({
      where: {
        name: {
          equals: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    // If not found, create new employer
    if (!employer) {
      employer = await this.prisma.employer.create({
        data: { name: cleanIdentifier },
      });
    }

    return employer.id;
  }

  /**
   * Helper method to resolve ISIC sector ID from identifier (UUID or name)
   * Creates new ISIC sector if name is provided and doesn't exist
   */
  private async resolveIsicSectorId(
    identifier?: string,
  ): Promise<string | undefined> {
    if (!identifier || identifier.trim() === '') {
      return undefined;
    }

    const cleanIdentifier = identifier.trim();

    // Check if it's a UUID
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanIdentifier)) {
      // Validate UUID exists
      const sector = await this.prisma.iSICSector.findUnique({
        where: { id: cleanIdentifier },
      });
      if (!sector) {
        throw new NotFoundException(
          `ISIC sector with ID '${cleanIdentifier}' not found`,
        );
      }
      return cleanIdentifier;
    }

    // Try to find by name
    let sector = await this.prisma.iSICSector.findFirst({
      where: {
        name: {
          equals: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    // If not found, create new ISIC sector
    if (!sector) {
      sector = await this.prisma.iSICSector.create({
        data: {
          name: cleanIdentifier,
          code: '', // Default empty code
        },
      });
    }

    return sector.id;
  }

  /**
   * Private helper method to get standardized include options for lead queries
   * Optimizes database queries by selecting only necessary fields
   */
  private getLeadIncludeOptions() {
    return {
      customer_category: true,
      isic_sector: true,
      branch: {
        include: {
          region: true,
        },
      },
      rm_user: true,
      employer: true,
      anchor_relationship: true,
      leads: true,
      contact_persons: {
        orderBy: {
          name: 'asc' as const,
        },
      },
    } as const;
  }

  /**
   * Private helper method to format lead response data
   * Transforms database result into standardized API response format
   */
  private formatLeadResponse(lead: any, includeCounts = false) {
    const response = {
      id: lead.id,
      customer_name: lead.customer_name,
      parent_lead_id: lead.parent_lead_id,
      phone_number: lead.phone_number,
      type_of_lead: lead.type_of_lead,
      client_id: lead.client_id,
      customer_category: lead.customer_category
        ? {
            id: lead.customer_category.id,
            name: lead.customer_category.name,
          }
        : null,
      isic_sector: lead.isic_sector
        ? {
            id: lead.isic_sector.id,
            name: lead.isic_sector.name,
          }
        : null,
      branch: lead.branch
        ? {
            id: lead.branch.id,
            name: lead.branch.name,
            region: lead.branch.region
              ? {
                  id: lead.branch.region.id,
                  name: lead.branch.region.name,
                }
              : null,
          }
        : null,
      rm_user: lead.rm_user
        ? {
            id: lead.rm_user.id,
            name: lead.rm_user.name,
            email: lead.rm_user.email,
            rm_code: lead.rm_user.rm_code,
          }
        : null,
      employer: lead.employer
        ? {
            id: lead.employer.id,
            name: lead.employer.name,
          }
        : null,
      parent_lead: lead.leads
        ? {
            id: lead.leads.id,
            name: lead.leads.customer_name,
          }
        : null,
      contact_persons: lead.contact_persons || [],
    };

    // Add counts if requested (for detailed view)
    if (includeCounts && lead._count) {
      return {
        ...response,
        referredLeadsCount: lead._count.other_leads,
        activitiesCount: lead._count.general_activities,
      };
    }

    return response;
  }

  /**
   * Formats a lead for summary response with activity counts
   */
  private async formatLeadSummaryResponse(
    lead: any,
  ): Promise<LeadSummaryResponseDto> {
    // Get activity counts for this lead
    const [visitCount, callCount, lastActivity, latestActivityWithOfficer] =
      await Promise.all([
        this.prisma.activity.count({
          where: {
            lead_id: lead.id,
            interaction_type: 'visit',
          },
        }),
        this.prisma.activity.count({
          where: {
            lead_id: lead.id,
            interaction_type: 'call',
          },
        }),
        this.prisma.activity.findFirst({
          where: {
            lead_id: lead.id,
          },
          orderBy: {
            created_at: 'desc',
          },
          select: {
            created_at: true,
            interaction_type: true,
          },
        }),
        this.prisma.activity.findFirst({
          where: {
            lead_id: lead.id,
          },
          orderBy: {
            created_at: 'desc',
          },
          select: {
            performed_by: {
              select: {
                name: true,
              },
            },
          },
        }),
      ]);

    return {
      id: lead.id,
      parent_lead_id: lead.parent_lead_id,
      anchor_relationship_id: lead.anchor_relationship_id,
      lead_name: lead.customer_name || 'Unknown Customer',
      status: lead.lead_status || null, // Return actual lead_status from database as 'status'
      phoneNumber: lead.phone_number || '',
      no_of_visits: visitCount,
      no_of_calls: callCount,
      last_interaction: lastActivity
        ? {
            interaction_type: lastActivity.interaction_type || 'unknown',
            date: lastActivity.created_at.toISOString(),
          }
        : null,
      officer: latestActivityWithOfficer?.performed_by?.name || null,
      customer_category: lead.customer_category
        ? {
            id: lead.customer_category.id,
            name: lead.customer_category.name,
          }
        : null,
      isic_sector: lead.isic_sector
        ? {
            id: lead.isic_sector.id,
            name: lead.isic_sector.name,
          }
        : null,
      branch: lead.branch
        ? {
            id: lead.branch.id,
            name: lead.branch.name,
            region: {
              id: lead.branch.region.id,
              name: lead.branch.region.name,
            },
          }
        : null,
      employerName: lead.employer?.name || null,
      parent_lead_name: lead.leads?.customer_name || null,
      anchor_relationship_name: lead.anchor_relationship?.name || null,
    };
  }

  /**
   * Converts a lead by assigning an account number and generating a client ID
   * @param id - UUID of the lead to convert
   * @param convertLeadDto - Data containing the account number
   * @returns Promise<ConvertLeadResponseDto> - The lead ID
   */
  async convertLead(
    id: string,
    convertLeadDto: ConvertLeadDto,
  ): Promise<ConvertLeadResponseDto> {
    const { account_number } = convertLeadDto;

    // Check if lead exists
    const existingLead = await this.prisma.lead.findUnique({
      where: { id },
    });

    if (!existingLead) {
      throw new NotFoundException(`Lead with ID '${id}' not found`);
    }

    // Check if account number is already in use
    const existingAccountNumber = await this.prisma.lead.findFirst({
      where: {
        account_number: account_number,
        id: { not: id }, // Exclude current lead
      },
    });

    if (existingAccountNumber) {
      throw new ConflictException(
        `Account number '${account_number}' is already in use`,
      );
    }

    // Generate client ID using simple algorithm
    const clientId = this.generateClientId();

    // Check if generated client ID already exists (very unlikely but good to check)
    const existingClientId = await this.prisma.lead.findFirst({
      where: {
        client_id: clientId,
      },
    });

    if (existingClientId) {
      // If by chance it exists, generate another one
      const newClientId = this.generateClientId();
      await this.prisma.lead.update({
        where: { id },
        data: {
          account_number: account_number,
          client_id: newClientId,
        },
      });
    } else {
      // Update the lead with account number and client ID
      await this.prisma.lead.update({
        where: { id },
        data: {
          account_number: account_number,
          client_id: clientId,
        },
      });
    }

    return {
      lead_id: id,
    };
  }

  /**
   * Generates a client ID in the format "CL865972"
   * Uses random 6-digit number
   */
  private generateClientId(): string {
    const randomNumber = Math.floor(100000 + Math.random() * 900000); // 6-digit number
    return `CL${randomNumber}`;
  }
}
