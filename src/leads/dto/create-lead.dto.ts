import {
  IsNotEmpty,
  <PERSON><PERSON>tring,
  IsUUID,
  IsO<PERSON>al,
  <PERSON><PERSON>ength,
  IsPhoneNumber,
  IsArray,
  ValidateNested,
  ArrayMinSize,
  IsDateString,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CreateLeadContactPersonDto } from './lead-contact-person.dto';
/**
 * Data Transfer Object for creating a new lead
 * Validates input data and provides API documentation
 */
export class CreateLeadDto {
  @ApiProperty({
    description: 'Name of the customer',
    example: 'ABC Manufacturing Ltd',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Customer name is required' })
  @IsString({ message: 'Customer name must be a string' })
  @MaxLength(255, { message: 'Customer name cannot exceed 255 characters' })
  customer_name: string;

  @ApiPropertyOptional({
    description: 'UUID of the parent lead (for referrals)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Parent lead ID must be a valid UUID' })
  parent_lead_id?: string;

  @ApiProperty({
    description: 'UUID of the customer category',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Customer category ID is required' })
  @IsUUID(4, { message: 'Customer category ID must be a valid UUID' })
  customer_category_id: string;

  @ApiProperty({
    description: 'UUID of the ISIC sector',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'ISIC sector ID is required' })
  @IsUUID(4, { message: 'ISIC sector ID must be a valid UUID' })
  isic_sector_id: string;

  @ApiProperty({
    description: 'Primary phone number of the customer',
    example: '+************',
    maxLength: 20,
  })
  @IsNotEmpty({ message: 'Phone number is required' })
  @IsString({ message: 'Phone number must be a string' })
  @MaxLength(20, { message: 'Phone number cannot exceed 20 characters' })
  phone_number: string;

  @ApiProperty({
    description: 'Type of lead (e.g., Cold, Warm, Hot, Referral)',
    example: 'Warm',
    maxLength: 50,
  })
  @IsNotEmpty({ message: 'Type of lead is required' })
  @IsString({ message: 'Type of lead must be a string' })
  @MaxLength(50, { message: 'Type of lead cannot exceed 50 characters' })
  type_of_lead: string;

  @ApiProperty({
    description: 'UUID of the branch',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Branch ID is required' })
  @IsUUID(4, { message: 'Branch ID must be a valid UUID' })
  branch_id: string;

  @ApiProperty({
    description: 'UUID of the relationship manager (user)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'RM user ID is required' })
  @IsUUID(4, { message: 'RM user ID must be a valid UUID' })
  rm_user_id: string;

  @ApiPropertyOptional({
    description: 'Client ID (unique identifier for existing clients)',
    example: 'CLI-2024-001',
    maxLength: 50,
  })
  @IsOptional()
  @IsString({ message: 'Client ID must be a string' })
  @MaxLength(50, { message: 'Client ID cannot exceed 50 characters' })
  client_id?: string;

  @ApiProperty({
    description: 'UUID of the employer',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Employer ID is required' })
  @IsUUID(4, { message: 'Employer ID must be a valid UUID' })
  employer_id: string;

  @ApiPropertyOptional({
    description: 'Array of contact persons for this lead',
    type: [CreateLeadContactPersonDto],
  })
  @IsOptional()
  @IsArray({ message: 'Contact persons must be an array' })
  @ValidateNested({ each: true })
  @Type(() => CreateLeadContactPersonDto)
  contact_persons?: CreateLeadContactPersonDto[];
}
