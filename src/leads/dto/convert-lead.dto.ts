import { IsS<PERSON>, <PERSON>N<PERSON>Empt<PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for converting a lead
 */
export class ConvertLeadDto {
  @ApiProperty({
    description: 'Account number to assign to the lead',
    example: 'ACC123456789',
    maxLength: 50,
  })
  @IsString({ message: 'Account number must be a string' })
  @IsNotEmpty({ message: 'Account number is required' })
  @MaxLength(50, { message: 'Account number cannot exceed 50 characters' })
  account_number: string;
}
