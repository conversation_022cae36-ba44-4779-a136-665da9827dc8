import { 
  IsNotEmpty, 
  <PERSON>S<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>,
  IsOptional
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for creating a lead contact person
 */
export class CreateLeadContactPersonDto {
  @ApiProperty({
    description: 'UUID of the lead this contact person belongs to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Lead ID is required' })
  @IsUUID(4, { message: 'Lead ID must be a valid UUID' })
  lead_id: string;

  @ApiProperty({
    description: 'Name of the contact person',
    example: '<PERSON>',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Contact person name is required' })
  @IsString({ message: 'Contact person name must be a string' })
  @MaxLength(255, { message: 'Contact person name cannot exceed 255 characters' })
  name: string;

  @ApiProperty({
    description: 'Phone number of the contact person',
    example: '+************',
    maxLength: 20,
  })
  @IsNotEmpty({ message: 'Contact person phone number is required' })
  @IsString({ message: 'Contact person phone number must be a string' })
  @MaxLength(20, { message: 'Contact person phone number cannot exceed 20 characters' })
  phone_number: string;
}

/**
 * Data Transfer Object for updating a lead contact person
 */
export class UpdateLeadContactPersonDto {
  @ApiPropertyOptional({
    description: 'Name of the contact person',
    example: 'John Doe',
    maxLength: 255,
  })
  @IsOptional()
  @IsString({ message: 'Contact person name must be a string' })
  @MaxLength(255, { message: 'Contact person name cannot exceed 255 characters' })
  name?: string;

  @ApiPropertyOptional({
    description: 'Phone number of the contact person',
    example: '+************',
    maxLength: 20,
  })
  @IsOptional()
  @IsString({ message: 'Contact person phone number must be a string' })
  @MaxLength(20, { message: 'Contact person phone number cannot exceed 20 characters' })
  phone_number?: string;
}

/**
 * Data Transfer Object for lead contact person response
 */
export class LeadContactPersonResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the contact person',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'UUID of the lead this contact person belongs to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  lead_id: string;

  @ApiProperty({
    description: 'Name of the contact person',
    example: 'John Doe',
  })
  name: string;

  @ApiProperty({
    description: 'Phone number of the contact person',
    example: '+************',
  })
  phone_number: string;
}
