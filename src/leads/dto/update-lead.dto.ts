import {
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>eng<PERSON>,
  IsDateS<PERSON>,
  ValidateIf,
  IsIn,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for updating a lead
 * All fields are optional for partial updates
 * Uses the same field names as CreateLeadNewDto for consistency
 */
export class UpdateLeadDto {
  @ApiPropertyOptional({
    description: 'UUID of the branch',
    example: '523b569f-30ae-46f4-bccb-f48c368b8a81',
  })
  @IsOptional()
  @ValidateIf((o) => o.branchId && o.branchId.trim() !== '')
  @IsUUID(4, { message: 'Branch ID must be a valid UUID' })
  branchId?: string;

  @ApiPropertyOptional({
    description: 'Client ID (unique identifier for existing clients)',
    example: 'CLI-2024-001',
    maxLength: 50,
  })
  @IsOptional()
  @IsString({ message: 'Client ID must be a string' })
  @MaxLength(50, { message: 'Client ID cannot exceed 50 characters' })
  clientId?: string;

  @ApiPropertyOptional({
    description: 'Name of the contact person',
    example: '<PERSON>',
    maxLength: 255,
  })
  @IsOptional()
  @IsString({ message: 'Contact person name must be a string' })
  @MaxLength(255, {
    message: 'Contact person name cannot exceed 255 characters',
  })
  contactPersonName?: string;

  @ApiPropertyOptional({
    description: 'Phone number of the contact person',
    example: '+************',
    maxLength: 20,
  })
  @IsOptional()
  @IsString({ message: 'Contact person phone must be a string' })
  @MaxLength(20, {
    message: 'Contact person phone cannot exceed 20 characters',
  })
  contactPersonPhone?: string;

  @ApiPropertyOptional({
    description: 'Date when the lead was created',
    example: '2025-07-28T12:15:01.512Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Created date must be a valid ISO date string' })
  createdDate?: string;

  @ApiPropertyOptional({
    description: 'UUID of the customer category',
    example: '5315bbfb-0215-415c-9ef0-9ecbe91248f2',
  })
  @IsOptional()
  @ValidateIf((o) => o.customerCategoryId && o.customerCategoryId.trim() !== '')
  @IsUUID(4, { message: 'Customer category ID must be a valid UUID' })
  customerCategoryId?: string;

  @ApiPropertyOptional({
    description: 'Name of the customer',
    example: 'Cole Palmer',
    maxLength: 255,
  })
  @IsOptional()
  @IsString({ message: 'Customer name must be a string' })
  @MaxLength(255, { message: 'Customer name cannot exceed 255 characters' })
  customerName?: string;

  @ApiPropertyOptional({
    description: 'UUID of the employer',
    example: 'b7728663-86d8-41a9-9238-8c3533a114a9',
  })
  @IsOptional()
  @ValidateIf((o) => o.employerId && o.employerId.trim() !== '')
  @IsUUID(4, { message: 'Employer ID must be a valid UUID' })
  employerId?: string;

  @ApiPropertyOptional({
    description: 'Name of the employer (alternative to employerId)',
    example: 'ABC Corporation',
    maxLength: 255,
  })
  @IsOptional()
  @IsString({ message: 'Employer name must be a string' })
  @MaxLength(255, { message: 'Employer name cannot exceed 255 characters' })
  employerName?: string;

  @ApiPropertyOptional({
    description: 'UUID of the ISIC sector',
    example: 'f71002e5-1eef-4c53-9096-e421c0d40bad',
  })
  @IsOptional()
  @ValidateIf((o) => o.isicSectorId && o.isicSectorId.trim() !== '')
  @IsUUID(4, { message: 'ISIC sector ID must be a valid UUID' })
  isicSectorId?: string;

  @ApiPropertyOptional({
    description: 'Type of lead - indicates if customer is new or existing',
    example: 'New',
    enum: ['New', 'Existing'],
  })
  @IsOptional()
  @IsString({ message: 'Lead type must be a string' })
  @IsIn(['New', 'Existing'], {
    message: 'Lead type must be either "New" or "Existing"',
  })
  leadType?: string;

  @ApiPropertyOptional({
    description: 'Primary phone number of the customer',
    example: '+************',
    maxLength: 20,
  })
  @IsOptional()
  @IsString({ message: 'Phone number must be a string' })
  @MaxLength(20, { message: 'Phone number cannot exceed 20 characters' })
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: 'UUID of the parent lead (for child leads)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @ValidateIf((o) => o.parentLeadId && o.parentLeadId.trim() !== '')
  @IsUUID(4, { message: 'Parent lead ID must be a valid UUID' })
  parentLeadId?: string;

  @ApiPropertyOptional({
    description:
      'Status of the lead - indicates lead temperature/qualification level',
    example: 'Pending',
    maxLength: 50,
  })
  @IsOptional()
  @IsString({ message: 'Lead status must be a string' })
  @MaxLength(50, { message: 'Lead status cannot exceed 50 characters' })
  leadStatus?: string;

  @ApiPropertyOptional({
    description: 'UUID of the anchor relationship',
    example: 'c7e41fac-ac4a-4659-9d80-9d23cd653a76',
  })
  @IsOptional()
  @ValidateIf(
    (o) => o.anchorRelationshipId && o.anchorRelationshipId.trim() !== '',
  )
  @IsUUID(4, { message: 'Anchor relationship ID must be a valid UUID' })
  anchorRelationshipId?: string;

  @ApiPropertyOptional({
    description: 'UUID of the anchor relationship (alternative field name)',
    example: 'c7e41fac-ac4a-4659-9d80-9d23cd653a76',
  })
  @IsOptional()
  @ValidateIf((o) => o.anchorId && o.anchorId.trim() !== '')
  @IsUUID(4, { message: 'Anchor ID must be a valid UUID' })
  anchorId?: string;
}
