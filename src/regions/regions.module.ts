import { Module } from '@nestjs/common';
import { RegionsService } from './regions.service';
import { RegionsController } from './regions.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  providers: [RegionsService],
  controllers: [RegionsController],
  exports: [RegionsService], // Export service for use in other modules
})
export class RegionsModule {}
