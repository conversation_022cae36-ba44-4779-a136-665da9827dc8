import { IsNotEmpty, IsString, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for creating a new region
 * Validates input data and provides API documentation
 */
export class CreateRegionDto {
  @ApiProperty({
    description: 'The name of the region',
    example: 'Central Region',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Region name is required' })
  @IsString({ message: 'Region name must be a string' })
  @MaxLength(255, { message: 'Region name cannot exceed 255 characters' })
  name: string;
}
