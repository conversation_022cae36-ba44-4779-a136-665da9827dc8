import { PartialType } from '@nestjs/mapped-types';
import { CreateRegionDto } from './create-region.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for updating an existing region
 * Extends CreateRegionDto but makes all fields optional
 */
export class UpdateRegionDto extends PartialType(CreateRegionDto) {
  @ApiPropertyOptional({
    description: 'The name of the region',
    example: 'Updated Central Region',
    maxLength: 255,
  })
  name?: string;
}
