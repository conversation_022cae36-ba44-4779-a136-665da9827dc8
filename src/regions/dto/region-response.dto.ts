import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for region response
 * Defines the structure of region data returned by the API
 */
export class RegionResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the region',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the region',
    example: 'Central Region',
  })
  name: string | null;

  @ApiPropertyOptional({
    description: 'Number of branches in this region',
    example: 5,
  })
  branchCount?: number;

  @ApiPropertyOptional({
    description: 'Timestamp when the region was created',
    example: '2024-01-15T10:30:00.000Z',
    type: 'string',
    format: 'date-time',
  })
  created_at?: Date | null;

  @ApiPropertyOptional({
    description: 'Timestamp when the region was last updated',
    example: '2024-01-20T14:45:00.000Z',
    type: 'string',
    format: 'date-time',
  })
  updated_at?: Date | null;
}
