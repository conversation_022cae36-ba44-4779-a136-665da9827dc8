import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateRegionDto } from './dto/create-region.dto';
import { UpdateRegionDto } from './dto/update-region.dto';
import { RegionResponseDto } from './dto/region-response.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { Prisma } from '@prisma/client';

/**
 * Service responsible for handling all region-related business logic
 * Implements CRUD operations with optimized database queries
 */
@Injectable()
export class RegionsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new region
   * @param createRegionDto - Data for creating the region
   * @returns Promise<RegionResponseDto> - The created region
   * @throws ConflictException if region name already exists
   */
  async create(createRegionDto: CreateRegionDto): Promise<RegionResponseDto> {
    try {
      // Check if region with same name already exists
      const existingRegion = await this.prisma.region.findFirst({
        where: {
          name: {
            equals: createRegionDto.name,
            mode: 'insensitive', // Case-insensitive comparison
          },
        },
      });

      if (existingRegion) {
        throw new ConflictException(`Region with name '${createRegionDto.name}' already exists`);
      }

      // Create the new region
      const region = await this.prisma.region.create({
        data: createRegionDto,
        include: {
          _count: {
            select: { branches: true }, // Get branch count for response
          },
        },
      });

      return {
        id: region.id,
        name: region.name,
        branchCount: region._count.branches,
        created_at: region.created_at,
        updated_at: region.updated_at,
      };
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error creating region:', error);
      throw new BadRequestException('Failed to create region');
    }
  }

  /**
   * Retrieves all regions with pagination and optional search
   * @param paginationDto - Pagination and search parameters
   * @returns Promise<PaginatedResponseDto<RegionResponseDto>> - Paginated list of regions
   */
  async findAll(paginationDto: PaginationDto): Promise<PaginatedResponseDto<RegionResponseDto>> {
    try {
      const { page = 1, limit = 10, search } = paginationDto;
      const skip = (page - 1) * limit;

      // Build where clause for search functionality
      const whereClause: Prisma.RegionWhereInput = search
        ? {
            name: {
              contains: search,
              mode: 'insensitive', // Case-insensitive search
            },
          }
        : {};


      // Execute both count and data queries in parallel for better performance
      const [regions, total] = await Promise.all([
        this.prisma.region.findMany({
          where: whereClause,
          skip,
          take: limit,
          include: {
            _count: {
              select: { branches: true }, // Include branch count
            },
          },
          orderBy: {
            name: 'asc', // Sort by name alphabetically
          },
        }),
        this.prisma.region.count({
          where: whereClause,
        }),
      ]);

      // Transform data to response DTOs
      const data = regions.map((region) => ({
        id: region.id,
        name: region.name,
        branchCount: region._count.branches,
        created_at: region.created_at,
        updated_at: region.updated_at,
      }));

      const totalPages = Math.ceil(total / limit);

      return {
        data,
        meta: {
          total,
          page,
          limit,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
        },
      };
    } catch (error) {
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error fetching regions:', error);
      throw new BadRequestException('Failed to fetch regions');
    }
  }

  /**
   * Retrieves a single region by ID
   * @param id - The UUID of the region
   * @returns Promise<RegionResponseDto> - The region data
   * @throws NotFoundException if region doesn't exist
   */
  async findOne(id: string): Promise<RegionResponseDto> {
    try {
      const region = await this.prisma.region.findUnique({
        where: { id },
        include: {
          _count: {
            select: { branches: true },
          },
        },
      });

      if (!region) {
        throw new NotFoundException(`Region with ID '${id}' not found`);
      }

      return {
        id: region.id,
        name: region.name,
        branchCount: region._count.branches,
        created_at: region.created_at,
        updated_at: region.updated_at,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error fetching region:', error);
      throw new BadRequestException('Failed to fetch region');
    }
  }

  /**
   * Updates an existing region
   * @param id - The UUID of the region to update
   * @param updateRegionDto - Data for updating the region
   * @returns Promise<RegionResponseDto> - The updated region
   * @throws NotFoundException if region doesn't exist
   * @throws ConflictException if new name conflicts with existing region
   */
  async update(id: string, updateRegionDto: UpdateRegionDto): Promise<RegionResponseDto> {
    // Check if region exists
    const existingRegion = await this.prisma.region.findUnique({
      where: { id },
    });

    if (!existingRegion) {
      throw new NotFoundException(`Region with ID '${id}' not found`);
    }

    // If name is being updated, check for conflicts
    if (updateRegionDto.name && updateRegionDto.name !== existingRegion.name) {
      const conflictingRegion = await this.prisma.region.findFirst({
        where: {
          name: {
            equals: updateRegionDto.name,
            mode: 'insensitive',
          },
          id: {
            not: id, // Exclude current region from conflict check
          },
        },
      });

      if (conflictingRegion) {
        throw new ConflictException(`Region with name '${updateRegionDto.name}' already exists`);
      }
    }

    try {
      const updatedRegion = await this.prisma.region.update({
        where: { id },
        data: updateRegionDto,
        include: {
          _count: {
            select: { branches: true },
          },
        },
      });

      return {
        id: updatedRegion.id,
        name: updatedRegion.name,
        branchCount: updatedRegion._count.branches,
        created_at: updatedRegion.created_at,
        updated_at: updatedRegion.updated_at,
      };
    } catch (error) {
      throw new BadRequestException('Failed to update region');
    }
  }

  /**
   * Deletes a region by ID
   * @param id - The UUID of the region to delete
   * @returns Promise<void>
   * @throws NotFoundException if region doesn't exist
   * @throws ConflictException if region has associated branches
   */
  async remove(id: string): Promise<void> {
    // Check if region exists and has branches
    const region = await this.prisma.region.findUnique({
      where: { id },
      include: {
        _count: {
          select: { branches: true },
        },
      },
    });

    if (!region) {
      throw new NotFoundException(`Region with ID '${id}' not found`);
    }

    // Prevent deletion if region has branches
    if (region._count.branches > 0) {
      throw new ConflictException(
        `Cannot delete region '${region.name}' because it has ${region._count.branches} associated branch(es). Please remove all branches first.`
      );
    }

    try {
      await this.prisma.region.delete({
        where: { id },
      });
    } catch (error) {
      throw new BadRequestException('Failed to delete region');
    }
  }

  /**
   * Retrieves all branches for a specific region
   * @param regionId - The UUID of the region
   * @param paginationDto - Pagination parameters
   * @returns Promise<PaginatedResponseDto<any>> - Paginated list of branches
   * @throws NotFoundException if region doesn't exist
   */
  async getRegionBranches(regionId: string, paginationDto: PaginationDto) {
    // Verify region exists
    const region = await this.prisma.region.findUnique({
      where: { id: regionId },
    });

    if (!region) {
      throw new NotFoundException(`Region with ID '${regionId}' not found`);
    }

    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    const whereClause: Prisma.BranchWhereInput = {
      region_id: regionId,
      ...(search && {
        name: {
          contains: search,
          mode: 'insensitive',
        },
      }),
    };

    const [branches, total] = await Promise.all([
      this.prisma.branch.findMany({
        where: whereClause,
        skip,
        take: limit,
        select: {
          id: true,
          name: true,
          region_id: true,
        },
        orderBy: {
          name: 'asc',
        },
      }),
      this.prisma.branch.count({
        where: whereClause,
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: branches,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }
}