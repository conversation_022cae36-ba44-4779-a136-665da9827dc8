import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
} from '@nestjs/swagger';
import { RegionsService } from './regions.service';
import { CreateRegionDto } from './dto/create-region.dto';
import { UpdateRegionDto } from './dto/update-region.dto';
import { RegionResponseDto } from './dto/region-response.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

/**
 * Controller handling all region-related HTTP endpoints
 * Provides RESTful API for region management
 */
@ApiTags('Regions')
@Controller('regions')
export class RegionsController {
  constructor(private readonly regionsService: RegionsService) {}

  /**
   * Creates a new region
   * POST /regions
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new region',
    description: 'Creates a new region with the provided name. Region names must be unique (case-insensitive).'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Region created successfully',
    type: RegionResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiConflictResponse({ description: 'Region name already exists' })
  async create(
    @Body(ValidationPipe) createRegionDto: CreateRegionDto
  ): Promise<RegionResponseDto> {
    return this.regionsService.create(createRegionDto);
  }

  /**
   * Retrieves all regions with pagination and search
   * GET /regions
   */
  @Get()
  @ApiOperation({
    summary: 'Get all regions',
    description: 'Retrieves a paginated list of regions with optional search functionality. Includes branch count for each region.'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10, max: 100)' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term for region names' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Regions retrieved successfully',
    type: PaginatedResponseDto<RegionResponseDto>,
  })
  async findAll(
    @Query(ValidationPipe) paginationDto: PaginationDto
  ): Promise<PaginatedResponseDto<RegionResponseDto>> {
    return this.regionsService.findAll(paginationDto);
  }

  /**
   * Retrieves a single region by ID
   * GET /regions/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get region by ID',
    description: 'Retrieves a single region by its UUID. Includes branch count and basic branch information.'
  })
  @ApiParam({ name: 'id', description: 'Region UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Region retrieved successfully',
    type: RegionResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Region not found' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<RegionResponseDto> {
    return this.regionsService.findOne(id);
  }

  /**
   * Updates an existing region
   * PATCH /regions/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update region',
    description: 'Updates an existing region. Only provided fields will be updated. Region names must remain unique.'
  })
  @ApiParam({ name: 'id', description: 'Region UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Region updated successfully',
    type: RegionResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Region not found' })
  @ApiConflictResponse({ description: 'Region name already exists' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateRegionDto: UpdateRegionDto
  ): Promise<RegionResponseDto> {
    return this.regionsService.update(id, updateRegionDto);
  }
  /**
   * Deletes a region
   * DELETE /regions/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete region',
    description: 'Deletes a region by ID. Cannot delete regions that have associated branches.'
  })
  @ApiParam({ name: 'id', description: 'Region UUID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Region deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Region not found' })
  @ApiConflictResponse({ description: 'Region has associated branches and cannot be deleted' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.regionsService.remove(id);
  }

  /**
   * Retrieves all branches for a specific region
   * GET /regions/:id/branches
   */
  @Get(':id/branches')
  @ApiOperation({
    summary: 'Get branches by region',
    description: 'Retrieves all branches belonging to a specific region with pagination and search.'
  })
  @ApiParam({ name: 'id', description: 'Region UUID' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10, max: 100)' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term for branch names' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Branches retrieved successfully',
  })
  @ApiNotFoundResponse({ description: 'Region not found' })
  async getRegionBranches(
    @Param('id', ParseUUIDPipe) id: string,
    @Query(ValidationPipe) paginationDto: PaginationDto
  ) {
    return this.regionsService.getRegionBranches(id, paginationDto);
  }
}
