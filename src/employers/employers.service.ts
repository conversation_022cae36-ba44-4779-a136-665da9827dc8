import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateEmployerDto } from './dto/create-employer.dto';
import { UpdateEmployerDto } from './dto/update-employer.dto';
import { EmployerResponseDto } from './dto/employer-response.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { Prisma } from '@prisma/client';

/**
 * Service responsible for handling all employer-related business logic
 * Implements CRUD operations with optimized database queries
 */
@Injectable()
export class EmployersService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new employer
   * @param createEmployerDto - Data for creating the employer
   * @returns Promise<EmployerResponseDto> - The created employer
   * @throws ConflictException if employer name already exists
   */
  async create(createEmployerDto: CreateEmployerDto): Promise<EmployerResponseDto> {
    try {
      // Check if employer with same name already exists
      const existingEmployer = await this.prisma.employer.findFirst({
        where: {
          name: {
            equals: createEmployerDto.name,
            mode: 'insensitive', // Case-insensitive comparison
          },
        },
      });

      if (existingEmployer) {
        throw new ConflictException(`Employer with name '${createEmployerDto.name}' already exists`);
      }

      // Create the new employer
      const employer = await this.prisma.employer.create({
        data: createEmployerDto,
        include: {
          _count: {
            select: { leads: true }, // Get lead count for response
          },
        },
      });

      return {
        id: employer.id,
        name: employer.name,
        leadCount: employer._count.leads,
      };
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to create employer');
    }
  }

  /**
   * Retrieves all employers with pagination and optional search
   * @param paginationDto - Pagination and search parameters
   * @returns Promise<PaginatedResponseDto<EmployerResponseDto>> - Paginated list of employers
   */
  async findAll(paginationDto: PaginationDto): Promise<PaginatedResponseDto<EmployerResponseDto>> {
    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    // Build where clause for search functionality
    const whereClause: Prisma.EmployerWhereInput = search
      ? {
          name: {
            contains: search,
            mode: 'insensitive', // Case-insensitive search
          },
        }
      : {};

    // Execute both count and data queries in parallel for better performance
    const [employers, total] = await Promise.all([
      this.prisma.employer.findMany({
        where: whereClause,
        skip,
        take: limit,
        include: {
          _count: {
            select: { leads: true }, // Include lead count
          },
        },
        orderBy: {
          name: 'asc', // Sort by name alphabetically
        },
      }),
      this.prisma.employer.count({
        where: whereClause,
      }),
    ]);

    // Transform data to response DTOs
    const data = employers.map((employer) => ({
      id: employer.id,
      name: employer.name,
      leadCount: employer._count.leads,
    }));

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Retrieves a single employer by ID
   * @param id - The UUID of the employer
   * @returns Promise<EmployerResponseDto> - The employer data
   * @throws NotFoundException if employer doesn't exist
   */
  async findOne(id: string): Promise<EmployerResponseDto> {
    const employer = await this.prisma.employer.findUnique({
      where: { id },
      include: {
        _count: {
          select: { leads: true },
        },
      },
    });

    if (!employer) {
      throw new NotFoundException(`Employer with ID '${id}' not found`);
    }

    return {
      id: employer.id,
      name: employer.name,
      leadCount: employer._count.leads,
    };
  }

  /**
   * Updates an existing employer
   * @param id - The UUID of the employer to update
   * @param updateEmployerDto - Data for updating the employer
   * @returns Promise<EmployerResponseDto> - The updated employer
   * @throws NotFoundException if employer doesn't exist
   * @throws ConflictException if new name conflicts with existing employer
   */
  async update(id: string, updateEmployerDto: UpdateEmployerDto): Promise<EmployerResponseDto> {
    // Check if employer exists
    const existingEmployer = await this.prisma.employer.findUnique({
      where: { id },
    });

    if (!existingEmployer) {
      throw new NotFoundException(`Employer with ID '${id}' not found`);
    }

    // If name is being updated, check for conflicts
    if (updateEmployerDto.name && updateEmployerDto.name !== existingEmployer.name) {
      const conflictingEmployer = await this.prisma.employer.findFirst({
        where: {
          name: {
            equals: updateEmployerDto.name,
            mode: 'insensitive',
          },
          id: {
            not: id, // Exclude current employer from conflict check
          },
        },
      });

      if (conflictingEmployer) {
        throw new ConflictException(`Employer with name '${updateEmployerDto.name}' already exists`);
      }
    }

    try {
      const updatedEmployer = await this.prisma.employer.update({
        where: { id },
        data: updateEmployerDto,
        include: {
          _count: {
            select: { leads: true },
          },
        },
      });

      return {
        id: updatedEmployer.id,
        name: updatedEmployer.name,
        leadCount: updatedEmployer._count.leads,
      };
    } catch (error) {
      throw new BadRequestException('Failed to update employer');
    }
  }

  /**
   * Deletes an employer by ID
   * @param id - The UUID of the employer to delete
   * @returns Promise<void>
   * @throws NotFoundException if employer doesn't exist
   * @throws ConflictException if employer has associated leads
   */
  async remove(id: string): Promise<void> {
    // Check if employer exists and has leads
    const employer = await this.prisma.employer.findUnique({
      where: { id },
      include: {
        _count: {
          select: { leads: true },
        },
      },
    });

    if (!employer) {
      throw new NotFoundException(`Employer with ID '${id}' not found`);
    }

    // Prevent deletion if employer has leads
    if (employer._count.leads > 0) {
      throw new ConflictException(
        `Cannot delete employer '${employer.name}' because it has ${employer._count.leads} associated lead(s). Please reassign or remove these leads first.`
      );
    }

    try {
      await this.prisma.employer.delete({
        where: { id },
      });
    } catch (error) {
      throw new BadRequestException('Failed to delete employer');
    }
  }
  /**
   * Retrieves all leads for a specific employer
   * @param employerId - The UUID of the employer
   * @param paginationDto - Pagination parameters
   * @returns Promise<PaginatedResponseDto<any>> - Paginated list of leads
   * @throws NotFoundException if employer doesn't exist
   */
  async getEmployerLeads(employerId: string, paginationDto: PaginationDto) {
    // Verify employer exists
    const employer = await this.prisma.employer.findUnique({
      where: { id: employerId },
    });

    if (!employer) {
      throw new NotFoundException(`Employer with ID '${employerId}' not found`);
    }

    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    const whereClause: Prisma.LeadWhereInput = {
      employer_id: employerId,
      ...(search && {
        OR: [
          {
            customer_name: {
              contains: search,
              mode: 'insensitive',
            },
          },
          {
            client_id: {
              contains: search,
              mode: 'insensitive',
            },
          },
          {
            phone_number: {
              contains: search,
              mode: 'insensitive',
            },
          },
          {
            type_of_lead: {
              contains: search,
              mode: 'insensitive',
            },
          },
        ],
      }),
    };

    const [leads, total] = await Promise.all([
      this.prisma.lead.findMany({
        where: whereClause,
        skip,
        take: limit,
        select: {
          id: true,
          customer_name: true,
          client_id: true,
          phone_number: true,
          type_of_lead: true,
          branch: {
            select: {
              id: true,
              name: true,
              region: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          customer_category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          customer_name: 'asc',
        },
      }),
      this.prisma.lead.count({
        where: whereClause,
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: leads,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }
}