import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
} from '@nestjs/swagger';
import { EmployersService } from './employers.service';
import { CreateEmployerDto } from './dto/create-employer.dto';
import { UpdateEmployerDto } from './dto/update-employer.dto';
import { EmployerResponseDto } from './dto/employer-response.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

/**
 * Controller handling all employer-related HTTP endpoints
 * Provides RESTful API for employer management
 */
@ApiTags('Employers')
@Controller('employers')
export class EmployersController {
  constructor(private readonly employersService: EmployersService) {}

  /**
   * Creates a new employer
   * POST /employers
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new employer',
    description: 'Creates a new employer with the provided name. Employer names must be unique (case-insensitive).'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Employer created successfully',
    type: EmployerResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiConflictResponse({ description: 'Employer name already exists' })
  async create(
    @Body(ValidationPipe) createEmployerDto: CreateEmployerDto
  ): Promise<EmployerResponseDto> {
    return this.employersService.create(createEmployerDto);
  }

  /**
   * Retrieves all employers with pagination and search
   * GET /employers
   */
  @Get()
  @ApiOperation({
    summary: 'Get all employers',
    description: 'Retrieves a paginated list of employers with optional search functionality. Includes lead count for each employer.'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10, max: 100)' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term for employer names' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Employers retrieved successfully',
    type: PaginatedResponseDto<EmployerResponseDto>,
  })
  async findAll(
    @Query(ValidationPipe) paginationDto: PaginationDto
  ): Promise<PaginatedResponseDto<EmployerResponseDto>> {
    return this.employersService.findAll(paginationDto);
  }

  /**
   * Retrieves a single employer by ID
   * GET /employers/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get employer by ID',
    description: 'Retrieves a single employer by its UUID. Includes lead count and basic employer information.'
  })
  @ApiParam({ name: 'id', description: 'Employer UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Employer retrieved successfully',
    type: EmployerResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Employer not found' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<EmployerResponseDto> {
    return this.employersService.findOne(id);
  }

  /**
   * Updates an existing employer
   * PATCH /employers/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update employer',
    description: 'Updates an existing employer. Only provided fields will be updated. Employer names must remain unique.'
  })
  @ApiParam({ name: 'id', description: 'Employer UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Employer updated successfully',
    type: EmployerResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Employer not found' })
  @ApiConflictResponse({ description: 'Employer name already exists' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateEmployerDto: UpdateEmployerDto
  ): Promise<EmployerResponseDto> {
    return this.employersService.update(id, updateEmployerDto);
  }

  /**
   * Deletes an employer
   * DELETE /employers/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete employer',
    description: 'Deletes an employer by ID. Cannot delete employers that have associated leads.'
  })
  @ApiParam({ name: 'id', description: 'Employer UUID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Employer deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Employer not found' })
  @ApiConflictResponse({ description: 'Employer has associated leads and cannot be deleted' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.employersService.remove(id);
  }

  /**
   * Retrieves all leads for a specific employer
   * GET /employers/:id/leads
   */
  @Get(':id/leads')
  @ApiOperation({
    summary: 'Get leads by employer',
    description: 'Retrieves all leads belonging to a specific employer with pagination and search. Includes comprehensive lead information with related data.'
  })
  @ApiParam({ name: 'id', description: 'Employer UUID' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10, max: 100)' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term for customer name, client ID, phone number, or lead type' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Leads retrieved successfully',
  })
  @ApiNotFoundResponse({ description: 'Employer not found' })
  async getEmployerLeads(
    @Param('id', ParseUUIDPipe) id: string,
    @Query(ValidationPipe) paginationDto: PaginationDto
  ) {
    return this.employersService.getEmployerLeads(id, paginationDto);
  }
}