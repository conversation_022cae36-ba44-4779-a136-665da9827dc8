import { IsNotEmpty, <PERSON>S<PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for creating a new employer
 * Validates input data and provides API documentation
 */
export class CreateEmployerDto {
  @ApiProperty({
    description: 'The name of the employer',
    example: 'ABC Corporation',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Employer name is required' })
  @IsString({ message: 'Employer name must be a string' })
  @MaxLength(255, { message: 'Employer name cannot exceed 255 characters' })
  name: string;
}
