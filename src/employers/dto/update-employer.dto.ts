import { PartialType } from '@nestjs/mapped-types';
import { CreateEmployerDto } from './create-employer.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for updating an existing employer
 * Extends CreateEmployerDto but makes all fields optional
 */
export class UpdateEmployerDto extends PartialType(CreateEmployerDto) {
  @ApiPropertyOptional({
    description: 'The name of the employer',
    example: 'Updated ABC Corporation',
    maxLength: 255,
  })
  name?: string;
}
