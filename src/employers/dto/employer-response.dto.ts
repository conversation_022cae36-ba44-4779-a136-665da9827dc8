import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for employer response
 * Defines the structure of employer data returned by the API
 */
export class EmployerResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the employer',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the employer',
    example: 'ABC Corporation',
    nullable: true,
  })
  name: string | null;

  @ApiProperty({
    description: 'Number of leads associated with this employer',
    example: 35,
  })
  leadCount?: number;
}
