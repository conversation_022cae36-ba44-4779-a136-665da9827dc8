import { Module } from '@nestjs/common';
import { EmployersService } from './employers.service';
import { EmployersController } from './employers.controller';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * Module for Employers functionality
 * Provides CRUD operations for employer management
 */
@Module({
  imports: [PrismaModule],
  providers: [EmployersService],
  controllers: [EmployersController],
  exports: [EmployersService], // Export service for use in other modules
})
export class EmployersModule {}
