import { IsNotEmpty, <PERSON>String, <PERSON>Length, IsOptional, Matches, IsArray, ArrayNotEmpty, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for creating a new permission
 *
 * This DTO validates input data for permission creation and provides
 * comprehensive API documentation for Swagger/OpenAPI.
 *
 * @class CreatePermissionDto
 */
export class CreatePermissionDto {
  @ApiProperty({
    description: 'Custom permission ID (e.g., "users.create", "reports.read", "admin.manage")',
    example: 'users.create',
    maxLength: 100,
    pattern: '^[a-z][a-z0-9]*(\.[a-z][a-z0-9]*)*$',
  })
  @IsNotEmpty({ message: 'Permission ID is required' })
  @IsString({ message: 'Permission ID must be a string' })
  @MaxLength(100, { message: 'Permission ID cannot exceed 100 characters' })
  @Matches(/^[a-z][a-z0-9]*(\.[a-z][a-z0-9]*)*$/, {
    message: 'Permission ID must be lowercase, start with a letter, and use dots for hierarchy (e.g., "users.create")'
  })
  id: string;

  @ApiProperty({
    description: 'Human-readable name of the permission',
    example: 'Create Users',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Permission name is required' })
  @IsString({ message: 'Permission name must be a string' })
  @MaxLength(255, { message: 'Permission name cannot exceed 255 characters' })
  name: string;

  @ApiPropertyOptional({
    description: 'Optional description explaining what this permission allows',
    example: 'Allows creating new user accounts in the system',
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Permission description must be a string' })
  @MaxLength(500, { message: 'Permission description cannot exceed 500 characters' })
  description?: string;
}

/**
 * Data Transfer Object for bulk permission creation
 *
 * This DTO allows creating multiple permissions in a single API call,
 * which is useful for initial system setup or bulk administrative tasks.
 *
 * @class BulkCreatePermissionsDto
 */
export class BulkCreatePermissionsDto {
  @ApiProperty({
    description: 'Array of permissions to create',
    type: [CreatePermissionDto],
    example: [
      {
        id: 'users.create',
        name: 'Create Users',
        description: 'Create new user accounts'
      },
      {
        id: 'users.read',
        name: 'View Users',
        description: 'View user profiles and lists'
      },
      {
        id: 'users.update',
        name: 'Update Users',
        description: 'Edit existing user accounts'
      }
    ],
  })
  @IsArray({ message: 'Permissions must be an array' })
  @ArrayNotEmpty({ message: 'At least one permission is required' })
  @ValidateNested({ each: true })
  @Type(() => CreatePermissionDto)
  permissions: CreatePermissionDto[];
}
