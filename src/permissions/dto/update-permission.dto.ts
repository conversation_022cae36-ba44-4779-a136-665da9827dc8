import { PartialType } from '@nestjs/mapped-types';
import { CreatePermissionDto } from './create-permission.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for updating an existing permission
 *
 * Extends CreatePermissionDto but makes all fields optional.
 * This allows for partial updates where only specific fields need to be modified.
 * Note: Permission ID cannot be updated after creation for data integrity.
 *
 * @class UpdatePermissionDto
 */
export class UpdatePermissionDto extends PartialType(CreatePermissionDto) {
  @ApiPropertyOptional({
    description: 'Human-readable name of the permission',
    example: 'Update Users',
    maxLength: 255,
  })
  name?: string;

  @ApiPropertyOptional({
    description: 'Optional description explaining what this permission allows',
    example: 'Allows updating existing user accounts in the system',
    maxLength: 500,
  })
  description?: string;
}
