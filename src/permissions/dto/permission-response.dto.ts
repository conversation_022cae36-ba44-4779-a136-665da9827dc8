import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for permission response
 * 
 * Defines the structure of permission data returned by the API.
 * This ensures consistent response format across all permission endpoints.
 * 
 * @class PermissionResponseDto
 */
export class PermissionResponseDto {
  @ApiProperty({
    description: 'Custom permission identifier',
    example: 'users.create',
  })
  id: string;

  @ApiProperty({
    description: 'Human-readable name of the permission',
    example: 'Create Users',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Optional description explaining what this permission allows',
    example: 'Allows creating new user accounts in the system',
    nullable: true,
  })
  description: string | null;

  @ApiPropertyOptional({
    description: 'Number of roles that have this permission',
    example: 3,
  })
  roleCount?: number;
}
