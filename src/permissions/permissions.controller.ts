import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
} from '@nestjs/swagger';
import { PermissionsService } from './permissions.service';
import { CreatePermissionDto, BulkCreatePermissionsDto } from './dto/create-permission.dto';
import { UpdatePermissionDto } from './dto/update-permission.dto';
import { PermissionResponseDto } from './dto/permission-response.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

/**
 * Permissions Controller
 * 
 * This controller handles all HTTP requests related to permission management.
 * It provides comprehensive CRUD operations with proper validation, error handling,
 * and API documentation for Swagger/OpenAPI.
 * 
 * All endpoints follow RESTful conventions and include:
 * - Input validation using DTOs
 * - Comprehensive error responses
 * - Pagination support for list operations
 * - Search functionality
 * - Detailed API documentation
 * 
 * @controller permissions
 */
@ApiTags('permissions')
@Controller('permissions')
export class PermissionsController {
  constructor(private readonly permissionsService: PermissionsService) {}

  /**
   * Creates a new permission
   * POST /permissions
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new permission',
    description: 'Creates a new permission with the provided name and optional description. Permission names must be unique (case-insensitive).'
  })
  @ApiResponse({
    status: 201,
    description: 'Permission created successfully',
    type: PermissionResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data or database connection failed' })
  @ApiConflictResponse({ description: 'Permission name already exists' })
  async create(
    @Body(ValidationPipe) createPermissionDto: CreatePermissionDto
  ): Promise<PermissionResponseDto> {
    return this.permissionsService.create(createPermissionDto);
  }

  /**
   * Creates multiple permissions at once
   * POST /permissions/bulk
   */
  @Post('bulk')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create multiple permissions at once',
    description: 'Creates multiple permissions in a single operation. Useful for initial setup or bulk administrative tasks. Returns detailed results including created permissions, skipped duplicates, and any errors.'
  })
  @ApiResponse({
    status: 201,
    description: 'Bulk permission creation completed',
    schema: {
      type: 'object',
      properties: {
        created: {
          type: 'array',
          items: { $ref: '#/components/schemas/PermissionResponseDto' },
          description: 'Successfully created permissions',
        },
        skipped: {
          type: 'array',
          items: { type: 'string' },
          description: 'Permissions that were skipped (already exist)',
          example: ['Permission ID \'users.create\' already exists'],
        },
        errors: {
          type: 'array',
          items: { type: 'string' },
          description: 'Errors encountered during creation',
          example: ['Failed to create permission \'invalid.id\': Validation failed'],
        },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Invalid input data, duplicate IDs/names in request, or database connection failed' })
  async bulkCreate(
    @Body(ValidationPipe) bulkCreatePermissionsDto: BulkCreatePermissionsDto
  ): Promise<{
    created: PermissionResponseDto[];
    skipped: string[];
    errors: string[];
  }> {
    return this.permissionsService.bulkCreate(bulkCreatePermissionsDto);
  }

  /**
   * Retrieves all permissions with pagination and search
   * GET /permissions
   */
  @Get()
  @ApiOperation({
    summary: 'Get all permissions',
    description: 'Retrieves a paginated list of permissions with optional search functionality. Search works on both name and description fields.'
  })
  @ApiResponse({
    status: 200,
    description: 'Permissions retrieved successfully',
    type: PaginatedResponseDto<PermissionResponseDto>,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page (default: 10, max: 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term to filter permissions by name or description',
    example: 'user',
  })
  @ApiBadRequestResponse({ description: 'Invalid query parameters or database connection failed' })
  async findAll(
    @Query(ValidationPipe) paginationDto: PaginationDto
  ): Promise<PaginatedResponseDto<PermissionResponseDto>> {
    return this.permissionsService.findAll(paginationDto);
  }

  /**
   * Gets all permissions without pagination
   * GET /permissions/all
   */
  @Get('all')
  @ApiOperation({
    summary: 'Get all permissions without pagination',
    description: 'Retrieves all permissions in the system without pagination. Useful for populating dropdowns, checkboxes, or getting complete permission lists. Supports optional search filtering.'
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Optional search term to filter permissions by name or description',
    example: 'user',
  })
  @ApiResponse({
    status: 200,
    description: 'All permissions retrieved successfully',
    type: [PermissionResponseDto],
  })
  @ApiBadRequestResponse({ description: 'Database connection failed' })
  async findAllNoPagination(
    @Query('search') search?: string
  ): Promise<PermissionResponseDto[]> {
    return this.permissionsService.findAllNoPagination(search);
  }

  /**
   * Retrieves a single permission by ID
   * GET /permissions/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get permission by ID',
    description: 'Retrieves a specific permission by its UUID, including the count of roles that have this permission.'
  })
  @ApiParam({
    name: 'id',
    description: 'Custom permission ID',
    example: 'users.create',
  })
  @ApiResponse({
    status: 200,
    description: 'Permission retrieved successfully',
    type: PermissionResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Permission not found' })
  @ApiBadRequestResponse({ description: 'Invalid permission ID format or database connection failed' })
  async findOne(
    @Param('id') id: string
  ): Promise<PermissionResponseDto> {
    return this.permissionsService.findOne(id);
  }

  /**
   * Updates an existing permission
   * PATCH /permissions/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update permission',
    description: 'Updates an existing permission. Only provided fields will be updated. Permission name must remain unique if changed.'
  })
  @ApiParam({
    name: 'id',
    description: 'Custom permission ID',
    example: 'users.create',
  })
  @ApiResponse({
    status: 200,
    description: 'Permission updated successfully',
    type: PermissionResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Permission not found' })
  @ApiBadRequestResponse({ description: 'Invalid input data or database connection failed' })
  @ApiConflictResponse({ description: 'Permission name already exists' })
  async update(
    @Param('id') id: string,
    @Body(ValidationPipe) updatePermissionDto: UpdatePermissionDto
  ): Promise<PermissionResponseDto> {
    return this.permissionsService.update(id, updatePermissionDto);
  }

  /**
   * Deletes a permission
   * DELETE /permissions/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete permission',
    description: 'Deletes a permission by ID. Cannot delete permissions that are assigned to roles - remove all role assignments first.'
  })
  @ApiParam({
    name: 'id',
    description: 'Custom permission ID',
    example: 'users.create',
  })
  @ApiResponse({
    status: 204,
    description: 'Permission deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Permission not found' })
  @ApiBadRequestResponse({ description: 'Invalid permission ID format or database connection failed' })
  @ApiConflictResponse({ description: 'Permission is assigned to roles and cannot be deleted' })
  async remove(@Param('id') id: string): Promise<void> {
    return this.permissionsService.remove(id);
  }

  /**
   * Retrieves all roles that have a specific permission
   * GET /permissions/:id/roles
   */
  @Get(':id/roles')
  @ApiOperation({
    summary: 'Get roles with permission',
    description: 'Retrieves a paginated list of roles that have the specified permission assigned.'
  })
  @ApiParam({
    name: 'id',
    description: 'Custom permission ID',
    example: 'users.create',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page (default: 10, max: 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term to filter roles by name or description',
    example: 'admin',
  })
  @ApiResponse({
    status: 200,
    description: 'Roles retrieved successfully',
  })
  @ApiNotFoundResponse({ description: 'Permission not found' })
  @ApiBadRequestResponse({ description: 'Invalid query parameters or database connection failed' })
  async getPermissionRoles(
    @Param('id') id: string,
    @Query(ValidationPipe) paginationDto: PaginationDto
  ) {
    return this.permissionsService.getPermissionRoles(id, paginationDto);
  }
}
