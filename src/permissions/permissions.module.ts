import { Module } from '@nestjs/common';
import { PermissionsService } from './permissions.service';
import { PermissionsController } from './permissions.controller';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * Permissions Module
 * 
 * This module handles all permission-related functionality including:
 * - CRUD operations for permissions
 * - Permission validation and management
 * - Integration with role-permission relationships
 * 
 * @module PermissionsModule
 */
@Module({
  imports: [PrismaModule],
  providers: [PermissionsService],
  controllers: [PermissionsController],
  exports: [PermissionsService], // Export service for use in other modules
})
export class PermissionsModule {}
