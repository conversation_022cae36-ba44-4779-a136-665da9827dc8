import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreatePermissionDto, BulkCreatePermissionsDto } from './dto/create-permission.dto';
import { UpdatePermissionDto } from './dto/update-permission.dto';
import { PermissionResponseDto } from './dto/permission-response.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { Prisma } from '@prisma/client';

/**
 * Service responsible for handling all permission-related business logic
 * 
 * This service implements comprehensive CRUD operations for permissions with:
 * - Optimized database queries using Prisma
 * - Proper error handling and validation
 * - Role count aggregation for enhanced responses
 * - Search and pagination functionality
 * 
 * @class PermissionsService
 */
@Injectable()
export class PermissionsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new permission
   * 
   * @param createPermissionDto - Data for creating the permission
   * @returns Promise<PermissionResponseDto> - The created permission with role count
   * @throws ConflictException if permission name already exists
   * @throws BadRequestException if database operation fails
   */
  async create(createPermissionDto: CreatePermissionDto): Promise<PermissionResponseDto> {
    try {
      // Check if permission with same ID already exists
      const existingPermissionById = await this.prisma.permission.findUnique({
        where: { id: createPermissionDto.id },
      });

      if (existingPermissionById) {
        throw new ConflictException(`Permission with ID '${createPermissionDto.id}' already exists`);
      }

      // Check if permission with same name already exists (case-insensitive)
      const existingPermissionByName = await this.prisma.permission.findFirst({
        where: {
          name: {
            equals: createPermissionDto.name,
            mode: 'insensitive',
          },
        },
      });

      if (existingPermissionByName) {
        throw new ConflictException(`Permission with name '${createPermissionDto.name}' already exists`);
      }

      // Create the new permission
      const permission = await this.prisma.permission.create({
        data: createPermissionDto,
        include: {
          _count: {
            select: { role_permissions: true }, // Get role count for response
          },
        },
      });

      return {
        id: permission.id,
        name: permission.name,
        description: permission.description,
        roleCount: permission._count.role_permissions,
      };
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }
      
      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      console.error('Error creating permission:', error);
      throw new BadRequestException('Failed to create permission');
    }
  }

  /**
   * Creates multiple permissions in a single operation
   *
   * @param bulkCreatePermissionsDto - Data for creating multiple permissions
   * @returns Promise<{ created: PermissionResponseDto[], skipped: string[], errors: string[] }> - Results of bulk operation
   * @throws BadRequestException if database operation fails
   */
  async bulkCreate(bulkCreatePermissionsDto: BulkCreatePermissionsDto): Promise<{
    created: PermissionResponseDto[];
    skipped: string[];
    errors: string[];
  }> {
    try {
      const { permissions } = bulkCreatePermissionsDto;
      const created: PermissionResponseDto[] = [];
      const skipped: string[] = [];
      const errors: string[] = [];

      // Extract all IDs and names for batch validation
      const permissionIds = permissions.map(p => p.id);
      const permissionNames = permissions.map(p => p.name);

      // Check for duplicates within the request
      const duplicateIds = permissionIds.filter((id, index) => permissionIds.indexOf(id) !== index);
      const duplicateNames = permissionNames.filter((name, index) => permissionNames.indexOf(name) !== index);

      if (duplicateIds.length > 0) {
        throw new BadRequestException(`Duplicate permission IDs in request: ${[...new Set(duplicateIds)].join(', ')}`);
      }

      if (duplicateNames.length > 0) {
        throw new BadRequestException(`Duplicate permission names in request: ${[...new Set(duplicateNames)].join(', ')}`);
      }

      // Check for existing permissions in database
      const [existingByIds, existingByNames] = await Promise.all([
        this.prisma.permission.findMany({
          where: { id: { in: permissionIds } },
          select: { id: true },
        }),
        this.prisma.permission.findMany({
          where: {
            name: {
              in: permissionNames,
              mode: 'insensitive',
            },
          },
          select: { id: true, name: true },
        }),
      ]);

      const existingIds = new Set(existingByIds.map(p => p.id));
      const existingNames = new Set(existingByNames.map(p => p.name.toLowerCase()));

      // Process each permission
      for (const permission of permissions) {
        try {
          // Skip if ID already exists
          if (existingIds.has(permission.id)) {
            skipped.push(`Permission ID '${permission.id}' already exists`);
            continue;
          }

          // Skip if name already exists
          if (existingNames.has(permission.name.toLowerCase())) {
            skipped.push(`Permission name '${permission.name}' already exists`);
            continue;
          }

          // Create the permission
          const newPermission = await this.prisma.permission.create({
            data: permission,
            include: {
              _count: {
                select: { role_permissions: true },
              },
            },
          });

          created.push({
            id: newPermission.id,
            name: newPermission.name,
            description: newPermission.description,
            roleCount: (newPermission as any)._count.role_permissions,
          });

          // Add to existing sets to prevent duplicates in subsequent iterations
          existingIds.add(permission.id);
          existingNames.add(permission.name.toLowerCase());

        } catch (error) {
          errors.push(`Failed to create permission '${permission.id}': ${error.message}`);
        }
      }

      return { created, skipped, errors };

    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error bulk creating permissions:', error);
      throw new BadRequestException('Failed to bulk create permissions');
    }
  }

  /**
   * Retrieves all permissions with pagination and optional search
   * 
   * @param paginationDto - Pagination and search parameters
   * @returns Promise<PaginatedResponseDto<PermissionResponseDto>> - Paginated list of permissions
   * @throws BadRequestException if database operation fails
   */
  async findAll(paginationDto: PaginationDto): Promise<PaginatedResponseDto<PermissionResponseDto>> {
    try {
      const { page = 1, limit = 10, search } = paginationDto;
      const skip = (page - 1) * limit;

      // Build where clause for search functionality
      const whereClause: Prisma.PermissionWhereInput = search
        ? {
            OR: [
              {
                name: {
                  contains: search,
                  mode: 'insensitive', // Case-insensitive search
                },
              },
              {
                description: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
            ],
          }
        : {};

      // Execute both count and data queries in parallel for better performance
      const [permissions, total] = await Promise.all([
        this.prisma.permission.findMany({
          where: whereClause,
          skip,
          take: limit,
          include: {
            _count: {
              select: { role_permissions: true }, // Include role count
            },
          },
          orderBy: {
            name: 'asc', // Sort by name alphabetically
          },
        }),
        this.prisma.permission.count({
          where: whereClause,
        }),
      ]);

      // Transform data to response DTOs
      const data = permissions.map((permission) => ({
        id: permission.id,
        name: permission.name,
        description: permission.description,
        roleCount: (permission as any)._count.role_permissions,
      }));

      const totalPages = Math.ceil(total / limit);

      return {
        data,
        meta: {
          total,
          page,
          limit,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
        },
      };
    } catch (error) {
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }
      
      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      console.error('Error fetching permissions:', error);
      throw new BadRequestException('Failed to fetch permissions');
    }
  }

  /**
   * Retrieves all permissions without pagination
   * Useful for populating dropdowns, checkboxes, or getting complete lists
   *
   * @param search - Optional search term to filter permissions
   * @returns Promise<PermissionResponseDto[]> - All permissions (or filtered results)
   * @throws BadRequestException if database operation fails
   */
  async findAllNoPagination(search?: string): Promise<PermissionResponseDto[]> {
    try {
      // Build where clause for search functionality
      const whereClause: Prisma.PermissionWhereInput = search
        ? {
            OR: [
              {
                name: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
              {
                description: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
            ],
          }
        : {};

      const permissions = await this.prisma.permission.findMany({
        where: whereClause,
        include: {
          _count: {
            select: { role_permissions: true },
          },
        },
        orderBy: {
          name: 'asc',
        },
      });

      // Transform data to response DTOs
      return permissions.map((permission) => ({
        id: permission.id,
        name: permission.name,
        description: permission.description,
        roleCount: (permission as any)._count.role_permissions,
      }));

    } catch (error) {
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error fetching all permissions:', error);
      throw new BadRequestException('Failed to fetch permissions');
    }
  }

  /**
   * Retrieves a single permission by ID
   *
   * @param id - The UUID of the permission
   * @returns Promise<PermissionResponseDto> - The permission data with role count
   * @throws NotFoundException if permission doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async findOne(id: string): Promise<PermissionResponseDto> {
    try {
      const permission = await this.prisma.permission.findUnique({
        where: { id },
        include: {
          _count: {
            select: { role_permissions: true },
          },
        },
      });

      if (!permission) {
        throw new NotFoundException(`Permission with ID '${id}' not found`);
      }

      return {
        id: permission.id,
        name: permission.name,
        description: permission.description,
        roleCount: (permission as any)._count.role_permissions,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error fetching permission:', error);
      throw new BadRequestException('Failed to fetch permission');
    }
  }

  /**
   * Updates an existing permission
   *
   * @param id - The UUID of the permission to update
   * @param updatePermissionDto - Data for updating the permission
   * @returns Promise<PermissionResponseDto> - The updated permission with role count
   * @throws NotFoundException if permission doesn't exist
   * @throws ConflictException if new name conflicts with existing permission
   * @throws BadRequestException if database operation fails
   */
  async update(id: string, updatePermissionDto: UpdatePermissionDto): Promise<PermissionResponseDto> {
    try {
      // Check if permission exists
      const existingPermission = await this.prisma.permission.findUnique({
        where: { id },
      });

      if (!existingPermission) {
        throw new NotFoundException(`Permission with ID '${id}' not found`);
      }

      // Remove ID from update data to prevent changes (ID should be immutable)
      const { id: _, ...updateData } = updatePermissionDto;

      // If name is being updated, check for conflicts
      if (updateData.name && updateData.name !== existingPermission.name) {
        const conflictingPermission = await this.prisma.permission.findFirst({
          where: {
            name: {
              equals: updateData.name,
              mode: 'insensitive',
            },
            id: {
              not: id, // Exclude current permission from conflict check
            },
          },
        });

        if (conflictingPermission) {
          throw new ConflictException(`Permission with name '${updateData.name}' already exists`);
        }
      }

      const updatedPermission = await this.prisma.permission.update({
        where: { id },
        data: updateData,
        include: {
          _count: {
            select: { role_permissions: true },
          },
        },
      });

      return {
        id: updatedPermission.id,
        name: updatedPermission.name,
        description: updatedPermission.description,
        roleCount: (updatedPermission as any)._count.role_permissions,
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error updating permission:', error);
      throw new BadRequestException('Failed to update permission');
    }
  }

  /**
   * Deletes a permission by ID
   *
   * @param id - The UUID of the permission to delete
   * @returns Promise<void>
   * @throws NotFoundException if permission doesn't exist
   * @throws ConflictException if permission is assigned to roles
   * @throws BadRequestException if database operation fails
   */
  async remove(id: string): Promise<void> {
    try {
      // Check if permission exists and has role assignments
      const permission = await this.prisma.permission.findUnique({
        where: { id },
        include: {
          _count: {
            select: { role_permissions: true },
          },
        },
      });

      if (!permission) {
        throw new NotFoundException(`Permission with ID '${id}' not found`);
      }

      // Prevent deletion if permission is assigned to roles
      if ((permission as any)._count.role_permissions > 0) {
        throw new ConflictException(
          `Cannot delete permission '${permission.name}' because it is assigned to ${(permission as any)._count.role_permissions} role(s). Please remove all role assignments first.`
        );
      }

      await this.prisma.permission.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error deleting permission:', error);
      throw new BadRequestException('Failed to delete permission');
    }
  }

  /**
   * Retrieves all roles that have a specific permission
   *
   * @param permissionId - The UUID of the permission
   * @param paginationDto - Pagination parameters
   * @returns Promise<PaginatedResponseDto<any>> - Paginated list of roles with this permission
   * @throws NotFoundException if permission doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async getPermissionRoles(permissionId: string, paginationDto: PaginationDto) {
    try {
      // Verify permission exists
      const permission = await this.prisma.permission.findUnique({
        where: { id: permissionId },
      });

      if (!permission) {
        throw new NotFoundException(`Permission with ID '${permissionId}' not found`);
      }

      const { page = 1, limit = 10, search } = paginationDto;
      const skip = (page - 1) * limit;

      const whereClause: Prisma.RoleWhereInput = {
        role_permissions: {
          some: {
            permission_id: permissionId,
          },
        },
        ...(search && {
          OR: [
            {
              name: {
                contains: search,
                mode: 'insensitive',
              },
            },
            {
              description: {
                contains: search,
                mode: 'insensitive',
              },
            },
          ],
        }),
      };

      const [roles, total] = await Promise.all([
        this.prisma.role.findMany({
          where: whereClause,
          skip,
          take: limit,
          select: {
            id: true,
            name: true,
            description: true,
          },
          orderBy: {
            name: 'asc',
          },
        }),
        this.prisma.role.count({
          where: whereClause,
        }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        data: roles,
        meta: {
          total,
          page,
          limit,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
        },
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error fetching permission roles:', error);
      throw new BadRequestException('Failed to fetch permission roles');
    }
  }
}
