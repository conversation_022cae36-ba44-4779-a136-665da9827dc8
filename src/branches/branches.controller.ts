import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
} from '@nestjs/swagger';
import { BranchesService } from './branches.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';
import { BranchResponseDto } from './dto/branch-response.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

/**
 * Controller handling all branch-related HTTP endpoints
 * Provides RESTful API for branch management
 */
@ApiTags('Branches')
@Controller('branches')
export class BranchesController {
  constructor(private readonly branchesService: BranchesService) {}

  /**
   * Creates a new branch
   * POST /branches
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new branch',
    description: 'Creates a new branch in a specific region. Branch names must be unique within each region.'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Branch created successfully',
    type: BranchResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Region not found' })
  @ApiConflictResponse({ description: 'Branch name already exists in this region' })
  async create(
    @Body(ValidationPipe) createBranchDto: CreateBranchDto
  ): Promise<BranchResponseDto> {
    return this.branchesService.create(createBranchDto);
  }

  /**
   * Retrieves all branches with pagination and search
   * GET /branches
   */
  @Get()
  @ApiOperation({
    summary: 'Get all branches',
    description: 'Retrieves a paginated list of branches with optional search functionality. Search works on both branch and region names.'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10, max: 100)' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term for branch or region names' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Branches retrieved successfully',
    type: PaginatedResponseDto<BranchResponseDto>,
  })
  async findAll(
    @Query(ValidationPipe) paginationDto: PaginationDto
  ): Promise<PaginatedResponseDto<BranchResponseDto>> {
    return this.branchesService.findAll(paginationDto);
  }

  /**
   * Retrieves a single branch by ID
   * GET /branches/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get branch by ID',
    description: 'Retrieves a single branch by its UUID. Includes region information.'
  })
  @ApiParam({ name: 'id', description: 'Branch UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Branch retrieved successfully',
    type: BranchResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Branch not found' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<BranchResponseDto> {
    return this.branchesService.findOne(id);
  }

  /**
   * Updates an existing branch
   * PATCH /branches/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update branch',
    description: 'Updates an existing branch. Can change name and/or region. Branch names must remain unique within each region.'
  })
  @ApiParam({ name: 'id', description: 'Branch UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Branch updated successfully',
    type: BranchResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Branch or region not found' })
  @ApiConflictResponse({ description: 'Branch name already exists in the target region' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateBranchDto: UpdateBranchDto
  ): Promise<BranchResponseDto> {
    return this.branchesService.update(id, updateBranchDto);
  }
  /**
   * Deletes a branch
   * DELETE /branches/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete branch',
    description: 'Deletes a branch by ID. Cannot delete branches that have associated users or leads.'
  })
  @ApiParam({ name: 'id', description: 'Branch UUID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Branch deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Branch not found' })
  @ApiConflictResponse({ description: 'Branch has associated users or leads and cannot be deleted' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.branchesService.remove(id);
  }
}
