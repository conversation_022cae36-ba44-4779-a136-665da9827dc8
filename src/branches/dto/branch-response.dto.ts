import { ApiProperty } from '@nestjs/swagger';
import { RegionResponseDto } from '../../regions/dto/region-response.dto';

/**
 * Data Transfer Object for branch response
 * Defines the structure of branch data returned by the API
 */
export class BranchResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the branch',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the branch',
    example: 'Main Street Branch',
  })
  name: string | null;

  @ApiProperty({
    description: 'The UUID of the region this branch belongs to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  region_id: string;

  @ApiProperty({
    description: 'The region this branch belongs to',
    type: RegionResponseDto,
  })
  region?: RegionResponseDto;
}
