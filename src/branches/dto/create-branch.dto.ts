import { IsNotEmpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for creating a new branch
 * Validates input data and provides API documentation
 */
export class CreateBranchDto {
  @ApiProperty({
    description: 'The name of the branch',
    example: 'Main Street Branch',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Branch name is required' })
  @IsString({ message: 'Branch name must be a string' })
  @MaxLength(255, { message: 'Branch name cannot exceed 255 characters' })
  name: string;

  @ApiProperty({
    description: 'The UUID of the region this branch belongs to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Region ID is required' })
  @IsUUID('4', { message: 'Region ID must be a valid UUID' })
  region_id: string;
}
