import { PartialType } from '@nestjs/mapped-types';
import { CreateBranchDto } from './create-branch.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for updating an existing branch
 * Extends CreateBranchDto but makes all fields optional
 */
export class UpdateBranchDto extends PartialType(CreateBranchDto) {
  @ApiPropertyOptional({
    description: 'The name of the branch',
    example: 'Updated Main Street Branch',
    maxLength: 255,
  })
  name?: string;

  @ApiPropertyOptional({
    description: 'The UUID of the region this branch belongs to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  region_id?: string;
}
