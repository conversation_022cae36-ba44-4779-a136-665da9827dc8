import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCallActivityDto } from './dto/create-call-activity.dto';
import { CreateVisitActivityDto } from './dto/create-visit-activity.dto';
import { CallActivityResponseDto } from './dto/call-activity-response.dto';
import { VisitActivityResponseDto } from './dto/visit-activity-response.dto';

/**
 * Service handling all activity-related business logic
 * Provides operations for creating and managing activities
 */
@Injectable()
export class ActivitiesService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new call activity with attachments
   * @param createCallActivityDto - Data for creating the call activity
   * @returns Promise<CallActivityResponseDto> - The created call activity
   */
  async createCallActivity(
    createCallActivityDto: CreateCallActivityDto,
  ): Promise<CallActivityResponseDto> {
    const {
      call_type,
      call_status,
      notes,
      follow_up_date,
      purpose_id,
      leadID,
      performed_by_user_id,
      attachments = [],
    } = createCallActivityDto;

    // Use the provided leadID directly
    const leadId = leadID;
    let performedByUserId = performed_by_user_id;

    if (!performedByUserId) {
      const firstUser = await this.prisma.user.findFirst();
      if (!firstUser) {
        throw new BadRequestException(
          'No users found in the system. Please provide a performed_by_user_id.',
        );
      }
      performedByUserId = firstUser.id;
    }

    // Validate that the lead exists
    const lead = await this.prisma.lead.findUnique({
      where: { id: leadId },
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID '${leadId}' not found`);
    }

    // Validate that the purpose exists
    const purpose = await this.prisma.purposeOfActivity.findUnique({
      where: { id: purpose_id },
    });

    if (!purpose) {
      throw new NotFoundException(`Purpose with ID '${purpose_id}' not found`);
    }

    // Validate that the user exists
    const user = await this.prisma.user.findUnique({
      where: { id: performedByUserId },
    });

    if (!user) {
      throw new NotFoundException(
        `User with ID '${performedByUserId}' not found`,
      );
    }

    try {
      // Create the activity with attachments in a transaction
      const activity = await this.prisma.$transaction(async (tx) => {
        // Create the activity
        const newActivity = await tx.activity.create({
          data: {
            lead_id: leadId,
            activity_type: call_type, // Store call_type in activity_type field
            interaction_type: 'call', // Set interaction_type to 'call'
            call_status: call_status,
            notes: notes || null,
            next_followup_date: follow_up_date
              ? new Date(follow_up_date)
              : null,
            purpose_id: purpose_id,
            performed_by_user_id: performedByUserId,
          },
        });

        // Create attachments if provided
        if (attachments && attachments.length > 0) {
          // For now, we'll store the file name as the file_url
          // In a real implementation, you'd upload the files to a storage service like AWS S3
          // and store the URL here. For now, we'll create a simple file path
          await tx.activityAttachment.createMany({
            data: attachments.map((attachment) => ({
              activity_id: newActivity.id,
              file_url: `uploads/activities/${newActivity.id}/${attachment.name}`,
            })),
          });
        }

        return newActivity;
      });

      // Fetch the created activity with all relationships
      const createdActivity = await this.prisma.activity.findUnique({
        where: { id: activity.id },
        include: {
          purpose: {
            select: {
              id: true,
              name: true,
            },
          },
          performed_by: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          attachments: {
            select: {
              id: true,
              file_url: true,
            },
          },
        },
      });

      if (!createdActivity) {
        throw new BadRequestException('Failed to retrieve created activity');
      }

      // Transform to response DTO
      return this.transformToCallActivityResponse(createdActivity);
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create call activity');
    }
  }

  /**
   * Private helper method to transform activity data to response DTO
   */
  private transformToCallActivityResponse(
    activity: any,
  ): CallActivityResponseDto {
    return {
      id: activity.id,
      leadId: activity.lead_id,
      activityType: activity.activity_type,
      interactionType: activity.interaction_type,
      callStatus: activity.call_status,
      notes: activity.notes,
      nextFollowupDate: activity.next_followup_date?.toISOString() || undefined,
      purposeId: activity.purpose_id,
      purpose: {
        id: activity.purpose.id,
        name: activity.purpose.name,
      },
      performedByUserId: activity.performed_by_user_id,
      performedBy: {
        id: activity.performed_by.id,
        name: activity.performed_by.name,
        email: activity.performed_by.email,
      },
      attachments: activity.attachments.map((attachment: any) => ({
        id: attachment.id,
        fileUrl: attachment.file_url,
      })),
      createdAt: activity.created_at.toISOString(),
      updatedAt: activity.updated_at.toISOString(),
    };
  }

  /**
   * Creates a new visit activity with attachments
   * @param createVisitActivityDto - Data for creating the visit activity
   * @returns Promise<VisitActivityResponseDto> - The created visit activity
   */
  async createVisitActivity(
    createVisitActivityDto: CreateVisitActivityDto,
  ): Promise<VisitActivityResponseDto> {
    const {
      visit_type,
      visit_status,
      notes,
      follow_up_date,
      purpose_id,
      leadID,
      performed_by_user_id,
      attachments = [],
    } = createVisitActivityDto;

    // Use the provided leadID directly
    const leadId = leadID;
    let performedByUserId = performed_by_user_id;

    if (!performedByUserId) {
      const firstUser = await this.prisma.user.findFirst();
      if (!firstUser) {
        throw new BadRequestException(
          'No users found in the system. Please provide a performed_by_user_id.',
        );
      }
      performedByUserId = firstUser.id;
    }

    // Validate that the lead exists
    const lead = await this.prisma.lead.findUnique({
      where: { id: leadId },
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID '${leadId}' not found`);
    }

    // Validate that the purpose exists
    const purpose = await this.prisma.purposeOfActivity.findUnique({
      where: { id: purpose_id },
    });

    if (!purpose) {
      throw new NotFoundException(`Purpose with ID '${purpose_id}' not found`);
    }

    // Validate that the user exists
    const user = await this.prisma.user.findUnique({
      where: { id: performedByUserId },
    });

    if (!user) {
      throw new NotFoundException(
        `User with ID '${performedByUserId}' not found`,
      );
    }

    try {
      // Create the activity with attachments in a transaction
      const activity = await this.prisma.$transaction(async (tx) => {
        // Create the activity
        const newActivity = await tx.activity.create({
          data: {
            lead_id: leadId,
            activity_type: visit_type, // Store visit_type in activity_type field
            interaction_type: 'visit', // Set interaction_type to 'visit'
            visit_status: visit_status, // Store in visit_status field
            notes: notes || null,
            next_followup_date: follow_up_date
              ? new Date(follow_up_date)
              : null,
            purpose_id: purpose_id,
            performed_by_user_id: performedByUserId,
          },
        });

        // Create attachments if provided
        if (attachments && attachments.length > 0) {
          // For now, we'll store the file name as the file_url
          // In a real implementation, you'd upload the files to a storage service like AWS S3
          // and store the URL here. For now, we'll create a simple file path
          await tx.activityAttachment.createMany({
            data: attachments.map((attachment) => ({
              activity_id: newActivity.id,
              file_url: `uploads/activities/${newActivity.id}/${attachment.name}`,
            })),
          });
        }

        return newActivity;
      });

      // Fetch the created activity with all relationships
      const createdActivity = await this.prisma.activity.findUnique({
        where: { id: activity.id },
        include: {
          purpose: {
            select: {
              id: true,
              name: true,
            },
          },
          performed_by: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          attachments: {
            select: {
              id: true,
              file_url: true,
            },
          },
        },
      });

      if (!createdActivity) {
        throw new BadRequestException('Failed to retrieve created activity');
      }

      // Transform to response DTO
      return this.transformToVisitActivityResponse(createdActivity);
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create visit activity');
    }
  }

  /**
   * Private helper method to transform visit activity data to response DTO
   */
  private transformToVisitActivityResponse(
    activity: any,
  ): VisitActivityResponseDto {
    return {
      id: activity.id,
      leadId: activity.lead_id,
      activityType: activity.activity_type,
      interactionType: activity.interaction_type,
      visitStatus: activity.visit_status,
      notes: activity.notes,
      nextFollowupDate: activity.next_followup_date?.toISOString() || undefined,
      purposeId: activity.purpose_id,
      purpose: {
        id: activity.purpose.id,
        name: activity.purpose.name,
      },
      performedByUserId: activity.performed_by_user_id,
      performedBy: {
        id: activity.performed_by.id,
        name: activity.performed_by.name,
        email: activity.performed_by.email,
      },
      attachments: activity.attachments.map((attachment: any) => ({
        id: attachment.id,
        fileUrl: attachment.file_url,
      })),
      createdAt: activity.created_at.toISOString(),
      updatedAt: activity.updated_at.toISOString(),
    };
  }

  /**
   * Gets interaction history for a specific lead
   * @param leadId - UUID of the lead
   * @returns Promise<InteractionHistoryDto[]> - Array of interaction history
   */
  async getLeadInteractionHistory(leadId: string) {
    // Validate that the lead exists
    const lead = await this.prisma.lead.findUnique({
      where: { id: leadId },
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID '${leadId}' not found`);
    }

    // Fetch all activities for the lead
    const activities = await this.prisma.activity.findMany({
      where: { lead_id: leadId },
      include: {
        performed_by: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        created_at: 'desc', // Most recent first
      },
    });

    // Transform to the required format
    return activities.map((activity) => ({
      interaction_type: activity.interaction_type || 'unknown',
      activity_type: activity.activity_type,
      next_followup_date: activity.next_followup_date?.toISOString() || null,
      notes: activity.notes,
      performed_by: activity.performed_by.name,
      created_at: activity.created_at.toISOString(),
    }));
  }
}
