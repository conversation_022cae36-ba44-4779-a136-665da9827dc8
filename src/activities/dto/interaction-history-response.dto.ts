import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for interaction history response
 */
export class InteractionHistoryResponseDto {
  @ApiProperty({
    description: 'Type of interaction (call, visit, etc.)',
    example: 'call',
  })
  interaction_type: string;

  @ApiProperty({
    description: 'Type of activity (First Contact, Follow up, etc.)',
    example: 'First Contact',
  })
  activity_type: string;

  @ApiPropertyOptional({
    description: 'Next follow up date and time',
    example: '2025-07-29T13:05:09.620Z',
  })
  next_followup_date: string | null;

  @ApiPropertyOptional({
    description: 'Notes about the interaction',
    example: 'He was rude but went well',
  })
  notes: string | null;

  @ApiProperty({
    description: 'Name of the user who performed this activity',
    example: 'Hildani',
  })
  performed_by: string;

  @ApiProperty({
    description: 'Date and time when the activity was created',
    example: '2025-07-29T13:05:09.620Z',
  })
  created_at: string;
}
