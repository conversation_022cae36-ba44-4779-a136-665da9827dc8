import {
  IsString,
  <PERSON><PERSON><PERSON>D,
  IsOptional,
  IsDateString,
  IsIn,
  MaxLength,
  IsArray,
  ValidateNested,
  IsNumber,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

/**
 * Data Transfer Object for file upload (reused from call activities)
 */
export class FileUploadDto {
  @ApiProperty({
    description: 'Name of the file',
    example: 'document.pdf',
  })
  @IsString({ message: 'File name must be a string' })
  @MaxLength(255, { message: 'File name cannot exceed 255 characters' })
  name: string;

  @ApiProperty({
    description: 'Size of the file in bytes',
    example: 1058339,
  })
  @IsNumber({}, { message: 'File size must be a number' })
  size: number;

  @ApiProperty({
    description: 'MIME type of the file',
    example: 'application/pdf',
  })
  @IsString({ message: 'File type must be a string' })
  @MaxLength(100, { message: 'File type cannot exceed 100 characters' })
  type: string;

  @ApiProperty({
    description: 'File object (will be processed by multer)',
    example: {},
  })
  file: any;
}

/**
 * Data Transfer Object for creating a visit activity
 */
export class CreateVisitActivityDto {
  @ApiProperty({
    description: 'Type of visit',
    example: 'First Visit',
    enum: ['First Visit', 'Follow up'],
  })
  @IsString({ message: 'Visit type must be a string' })
  @IsIn(['First Visit', 'Follow up'], {
    message: 'Visit type must be either "First Visit" or "Follow up"',
  })
  visit_type: string;

  @ApiProperty({
    description: 'Status of the visit',
    example: 'Successful',
    enum: ['Successful', 'Declined'],
  })
  @IsString({ message: 'Visit status must be a string' })
  @IsIn(['Successful', 'Declined'], {
    message: 'Visit status must be either "Successful" or "Declined"',
  })
  visit_status: string;

  @ApiPropertyOptional({
    description: 'Notes about the visit',
    example: 'Customer showed interest in our loan products',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  @MaxLength(1000, { message: 'Notes cannot exceed 1000 characters' })
  notes?: string;

  @ApiPropertyOptional({
    description: 'Follow up date and time',
    example: '2025-08-15T10:30:00.000Z',
  })
  @IsOptional()
  @IsDateString(
    {},
    { message: 'Follow up date must be a valid ISO date string' },
  )
  follow_up_date?: string;

  @ApiProperty({
    description: 'UUID of the purpose of this activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID(4, { message: 'Purpose ID must be a valid UUID' })
  purpose_id: string;

  @ApiProperty({
    description: 'UUID of the lead this activity is associated with',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID(4, { message: 'Lead ID must be a valid UUID' })
  leadID: string;

  @ApiPropertyOptional({
    description: 'UUID of the user performing this activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Performed by user ID must be a valid UUID' })
  performed_by_user_id?: string;

  @ApiPropertyOptional({
    description: 'Array of file attachments',
    type: [FileUploadDto],
  })
  @IsOptional()
  @IsArray({ message: 'Attachments must be an array' })
  @ValidateNested({ each: true })
  @Type(() => FileUploadDto)
  attachments?: FileUploadDto[];
}
