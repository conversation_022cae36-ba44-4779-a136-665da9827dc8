import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for activity attachment response
 */
export class ActivityAttachmentResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the attachment',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'File URL or path',
    example: 'https://example.com/files/document.pdf',
  })
  fileUrl: string;
}

/**
 * Data Transfer Object for call activity response
 */
export class CallActivityResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'UUID of the lead this activity is associated with',
    example: '550e8400-e29b-41d4-a716-************',
  })
  leadId: string;

  @ApiProperty({
    description: 'Type of activity (always "call" for call activities)',
    example: 'call',
  })
  activityType: string;

  @ApiProperty({
    description: 'Type of call interaction',
    example: 'First Contact',
  })
  interactionType: string;

  @ApiProperty({
    description: 'Status of the call',
    example: 'Success',
  })
  callStatus: string;

  @ApiPropertyOptional({
    description: 'Notes about the call',
    example: 'Customer showed interest in our loan products',
  })
  notes?: string;

  @ApiPropertyOptional({
    description: 'Follow up date and time',
    example: '2025-08-15T10:30:00.000Z',
  })
  nextFollowupDate?: string;

  @ApiProperty({
    description: 'UUID of the purpose of this activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  purposeId: string;

  @ApiProperty({
    description: 'Purpose information',
  })
  purpose: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'UUID of the user who performed this activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  performedByUserId: string;

  @ApiProperty({
    description: 'User who performed this activity',
  })
  performedBy: {
    id: string;
    name: string;
    email: string;
  };

  @ApiProperty({
    description: 'Array of file attachments',
    type: [ActivityAttachmentResponseDto],
  })
  attachments: ActivityAttachmentResponseDto[];

  @ApiProperty({
    description: 'Date and time when the activity was created',
    example: '2025-07-30T10:30:00.000Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Date and time when the activity was last updated',
    example: '2025-07-30T10:30:00.000Z',
  })
  updatedAt: string;
}
