import {
  IsString,
  <PERSON>UUI<PERSON>,
  <PERSON><PERSON>ptional,
  IsDateString,
  IsIn,
  Max<PERSON>ength,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

/**
 * Data Transfer Object for creating a call activity from FormData
 * This handles the form data sent from the frontend
 */
export class CreateCallActivityFormDto {
  @ApiProperty({
    description: 'Type of call',
    example: 'First Contact',
    enum: ['First Contact', 'Follow Up'],
  })
  @IsString({ message: 'Call type must be a string' })
  @IsIn(['First Contact', 'Follow Up'], {
    message: 'Call type must be either "First Contact" or "Follow Up"',
  })
  call_type: string;

  @ApiProperty({
    description: 'Status of the call',
    example: 'Success',
    enum: ['Success', 'Declined'],
  })
  @IsString({ message: 'Call status must be a string' })
  @IsIn(['Success', 'Declined'], {
    message: 'Call status must be either "Success" or "Declined"',
  })
  call_status: string;

  @ApiPropertyOptional({
    description: 'Notes about the call',
    example: 'Customer showed interest in our loan products',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  @MaxLength(1000, { message: 'Notes cannot exceed 1000 characters' })
  notes?: string;

  @ApiPropertyOptional({
    description: 'Follow up date and time',
    example: '2025-08-15T10:30:00.000Z',
  })
  @IsOptional()
  @IsDateString(
    {},
    { message: 'Follow up date must be a valid ISO date string' },
  )
  follow_up_date?: string;

  @ApiProperty({
    description: 'UUID of the purpose of this activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID(4, { message: 'Purpose ID must be a valid UUID' })
  purpose_id: string;

  @ApiProperty({
    description: 'UUID of the lead this activity is associated with',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID(4, { message: 'Lead ID must be a valid UUID' })
  leadID: string;

  @ApiPropertyOptional({
    description: 'UUID of the user performing this activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Performed by user ID must be a valid UUID' })
  performed_by_user_id?: string;
}
