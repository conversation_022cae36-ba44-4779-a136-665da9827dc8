import {
  IsString,
  IsUUID,
  <PERSON>Optional,
  IsDateString,
  IsIn,
  MaxLength,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for creating a visit activity from FormData
 * This handles the form data sent from the frontend for visit activities
 */
export class CreateVisitActivityFormDto {
  @ApiProperty({
    description: 'Type of visit',
    example: 'First Visit',
    enum: ['First Visit', 'Follow up'],
  })
  @IsString({ message: 'Visit type must be a string' })
  @IsIn(['First Visit', 'Follow up'], {
    message: 'Visit type must be either "First Visit" or "Follow up"',
  })
  visit_type: string;

  @ApiProperty({
    description: 'Status of the visit',
    example: 'Successful',
    enum: ['Successful', 'Declined'],
  })
  @IsString({ message: 'Visit status must be a string' })
  @IsIn(['Successful', 'Declined'], {
    message: 'Visit status must be either "Successful" or "Declined"',
  })
  visit_status: string;

  @ApiPropertyOptional({
    description: 'Notes about the visit',
    example: 'Customer showed interest in our loan products',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  @MaxLength(1000, { message: 'Notes cannot exceed 1000 characters' })
  notes?: string;

  @ApiPropertyOptional({
    description: 'Follow up date and time',
    example: '2025-08-15T10:30:00.000Z',
  })
  @IsOptional()
  @IsDateString(
    {},
    { message: 'Follow up date must be a valid ISO date string' },
  )
  follow_up_date?: string;

  @ApiProperty({
    description: 'UUID of the purpose of this activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID(4, { message: 'Purpose ID must be a valid UUID' })
  purpose_id: string;

  @ApiProperty({
    description: 'UUID of the lead this activity is associated with',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID(4, { message: 'Lead ID must be a valid UUID' })
  leadID: string;

  @ApiPropertyOptional({
    description: 'UUID of the user performing this activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Performed by user ID must be a valid UUID' })
  performed_by_user_id?: string;
}
