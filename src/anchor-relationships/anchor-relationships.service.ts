import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateAnchorRelationshipDto } from './dto/create-anchor-relationship.dto';
import { UpdateAnchorRelationshipDto } from './dto/update-anchor-relationship.dto';
import { AnchorRelationshipResponseDto } from './dto/anchor-relationship-response.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { Prisma } from '@prisma/client';

/**
 * Service responsible for handling all anchor relationship-related business logic
 * Implements CRUD operations with optimized database queries
 */
@Injectable()
export class AnchorRelationshipsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new anchor relationship
   * Validates that the name is unique (case-insensitive)
   */
  async create(createAnchorRelationshipDto: CreateAnchorRelationshipDto): Promise<AnchorRelationshipResponseDto> {
    const { name } = createAnchorRelationshipDto;

    try {
      // Check if anchor relationship with the same name already exists (case-insensitive)
      const existingAnchorRelationship = await this.prisma.anchorRelationship.findFirst({
        where: {
          name: {
            equals: name,
            mode: 'insensitive',
          },
        },
      });

      if (existingAnchorRelationship) {
        throw new ConflictException(`Anchor relationship with name '${name}' already exists`);
      }

      // Create the anchor relationship
      const anchorRelationship = await this.prisma.anchorRelationship.create({
        data: {
          name,
        },
      });

      return {
        id: anchorRelationship.id,
        name: anchorRelationship.name,
        leadCount: 0, // New anchor relationship has no leads initially
      };
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to create anchor relationship');
    }
  }

  /**
   * Retrieves all anchor relationships with pagination and search
   * Includes lead count for each anchor relationship
   */
  async findAll(paginationDto: PaginationDto): Promise<PaginatedResponseDto<AnchorRelationshipResponseDto>> {
    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    // Build where clause for search functionality
    const whereClause: Prisma.AnchorRelationshipWhereInput = search
      ? {
          name: {
            contains: search,
            mode: 'insensitive',
          },
        }
      : {};

    try {
      // Execute both count and data queries in parallel for better performance
      const [anchorRelationships, total] = await Promise.all([
        this.prisma.anchorRelationship.findMany({
          where: whereClause,
          skip,
          take: limit,
          orderBy: {
            name: 'asc',
          },
          include: {
            _count: {
              select: {
                leads: true,
              },
            },
          },
        }),
        this.prisma.anchorRelationship.count({
          where: whereClause,
        }),
      ]);

      // Transform data to response DTOs
      const data = anchorRelationships.map((anchorRelationship) => ({
        id: anchorRelationship.id,
        name: anchorRelationship.name,
        leadCount: anchorRelationship._count.leads,
      }));

      const totalPages = Math.ceil(total / limit);

      return {
        data,
        meta: {
          total,
          page,
          limit,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
        },
      };
    } catch (error) {
      throw new BadRequestException('Failed to retrieve anchor relationships');
    }
  }

  /**
   * Retrieves a single anchor relationship by ID
   * Includes lead count
   */
  async findOne(id: string): Promise<AnchorRelationshipResponseDto> {
    try {
      const anchorRelationship = await this.prisma.anchorRelationship.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              leads: true,
            },
          },
        },
      });

      if (!anchorRelationship) {
        throw new NotFoundException(`Anchor relationship with ID '${id}' not found`);
      }

      return {
        id: anchorRelationship.id,
        name: anchorRelationship.name,
        leadCount: anchorRelationship._count.leads,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to retrieve anchor relationship');
    }
  }

  /**
   * Updates an existing anchor relationship
   * Validates that the new name is unique (case-insensitive) if provided
   */
  async update(id: string, updateAnchorRelationshipDto: UpdateAnchorRelationshipDto): Promise<AnchorRelationshipResponseDto> {
    const { name } = updateAnchorRelationshipDto;

    try {
      // Check if anchor relationship exists
      const existingAnchorRelationship = await this.prisma.anchorRelationship.findUnique({
        where: { id },
      });

      if (!existingAnchorRelationship) {
        throw new NotFoundException(`Anchor relationship with ID '${id}' not found`);
      }

      // If name is being updated, check for uniqueness
      if (name && name !== existingAnchorRelationship.name) {
        const duplicateAnchorRelationship = await this.prisma.anchorRelationship.findFirst({
          where: {
            name: {
              equals: name,
              mode: 'insensitive',
            },
            NOT: {
              id,
            },
          },
        });

        if (duplicateAnchorRelationship) {
          throw new ConflictException(`Anchor relationship with name '${name}' already exists`);
        }
      }

      // Update the anchor relationship
      const updatedAnchorRelationship = await this.prisma.anchorRelationship.update({
        where: { id },
        data: updateAnchorRelationshipDto,
        include: {
          _count: {
            select: {
              leads: true,
            },
          },
        },
      });

      return {
        id: updatedAnchorRelationship.id,
        name: updatedAnchorRelationship.name,
        leadCount: updatedAnchorRelationship._count.leads,
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to update anchor relationship');
    }
  }

  /**
   * Deletes an anchor relationship
   * Prevents deletion if there are associated leads
   */
  async remove(id: string): Promise<void> {
    try {
      // Check if anchor relationship exists
      const anchorRelationship = await this.prisma.anchorRelationship.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              leads: true,
            },
          },
        },
      });

      if (!anchorRelationship) {
        throw new NotFoundException(`Anchor relationship with ID '${id}' not found`);
      }

      // Check if there are associated leads
      if (anchorRelationship._count.leads > 0) {
        throw new ConflictException(
          `Cannot delete anchor relationship '${anchorRelationship.name}' because it has ${anchorRelationship._count.leads} associated lead(s)`
        );
      }

      // Delete the anchor relationship
      await this.prisma.anchorRelationship.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to delete anchor relationship');
    }
  }
}
