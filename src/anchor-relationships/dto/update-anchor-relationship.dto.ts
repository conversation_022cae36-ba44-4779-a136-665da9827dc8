import { PartialType } from '@nestjs/mapped-types';
import { CreateAnchorRelationshipDto } from './create-anchor-relationship.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for updating an existing anchor relationship
 * Extends CreateAnchorRelationshipDto but makes all fields optional
 */
export class UpdateAnchorRelationshipDto extends PartialType(CreateAnchorRelationshipDto) {
  @ApiPropertyOptional({
    description: 'The name of the anchor relationship',
    example: 'Updated Strategic Partnership',
    maxLength: 255,
  })
  name?: string;
}
