import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for anchor relationship response
 * Defines the structure of anchor relationship data returned by the API
 */
export class AnchorRelationshipResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the anchor relationship',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the anchor relationship',
    example: 'Strategic Partnership',
    nullable: true,
  })
  name: string | null;

  @ApiProperty({
    description: 'Number of leads associated with this anchor relationship',
    example: 15,
  })
  leadCount?: number;
}
