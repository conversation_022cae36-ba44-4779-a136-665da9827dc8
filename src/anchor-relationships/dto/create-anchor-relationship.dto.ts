import { IsNotEmpty, <PERSON>S<PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for creating a new anchor relationship
 * Validates input data and provides API documentation
 */
export class CreateAnchorRelationshipDto {
  @ApiProperty({
    description: 'The name of the anchor relationship',
    example: 'Strategic Partnership',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Anchor relationship name is required' })
  @IsString({ message: 'Anchor relationship name must be a string' })
  @MaxLength(255, { message: 'Anchor relationship name cannot exceed 255 characters' })
  name: string;
}
