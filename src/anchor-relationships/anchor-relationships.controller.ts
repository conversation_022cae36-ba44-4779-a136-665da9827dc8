import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
} from '@nestjs/swagger';
import { AnchorRelationshipsService } from './anchor-relationships.service';
import { CreateAnchorRelationshipDto } from './dto/create-anchor-relationship.dto';
import { UpdateAnchorRelationshipDto } from './dto/update-anchor-relationship.dto';
import { AnchorRelationshipResponseDto } from './dto/anchor-relationship-response.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

/**
 * Controller handling all anchor relationship-related HTTP endpoints
 * Provides RESTful API for anchor relationship management
 */
@ApiTags('Anchor Relationships')
@Controller('anchor-relationships')
export class AnchorRelationshipsController {
  constructor(private readonly anchorRelationshipsService: AnchorRelationshipsService) {}

  /**
   * Creates a new anchor relationship
   * POST /anchor-relationships
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new anchor relationship',
    description: 'Creates a new anchor relationship with the provided name. The name must be unique (case-insensitive).',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Anchor relationship created successfully',
    type: AnchorRelationshipResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiConflictResponse({ description: 'Anchor relationship name already exists' })
  async create(
    @Body(ValidationPipe) createAnchorRelationshipDto: CreateAnchorRelationshipDto,
  ): Promise<AnchorRelationshipResponseDto> {
    return this.anchorRelationshipsService.create(createAnchorRelationshipDto);
  }

  /**
   * Retrieves all anchor relationships with pagination and search
   * GET /anchor-relationships
   */
  @Get()
  @ApiOperation({
    summary: 'Get all anchor relationships',
    description: 'Retrieves a paginated list of anchor relationships with optional search functionality. Search works on the name field. Includes lead count for each anchor relationship.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (default: 10, max: 100)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term to filter anchor relationships by name',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Anchor relationships retrieved successfully',
    type: PaginatedResponseDto<AnchorRelationshipResponseDto>,
  })
  @ApiBadRequestResponse({ description: 'Invalid query parameters' })
  async findAll(@Query() paginationDto: PaginationDto): Promise<PaginatedResponseDto<AnchorRelationshipResponseDto>> {
    return this.anchorRelationshipsService.findAll(paginationDto);
  }

  /**
   * Retrieves a single anchor relationship by ID
   * GET /anchor-relationships/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get anchor relationship by ID',
    description: 'Retrieves a single anchor relationship by its UUID. Includes lead count.',
  })
  @ApiParam({ name: 'id', description: 'Anchor relationship UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Anchor relationship retrieved successfully',
    type: AnchorRelationshipResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Anchor relationship not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<AnchorRelationshipResponseDto> {
    return this.anchorRelationshipsService.findOne(id);
  }

  /**
   * Updates an existing anchor relationship
   * PATCH /anchor-relationships/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update anchor relationship',
    description: 'Updates an existing anchor relationship by ID. The name must be unique (case-insensitive) if provided.',
  })
  @ApiParam({ name: 'id', description: 'Anchor relationship UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Anchor relationship updated successfully',
    type: AnchorRelationshipResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Anchor relationship not found' })
  @ApiConflictResponse({ description: 'Anchor relationship name already exists' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateAnchorRelationshipDto: UpdateAnchorRelationshipDto,
  ): Promise<AnchorRelationshipResponseDto> {
    return this.anchorRelationshipsService.update(id, updateAnchorRelationshipDto);
  }

  /**
   * Deletes an anchor relationship
   * DELETE /anchor-relationships/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete anchor relationship',
    description: 'Deletes an anchor relationship by ID. Cannot delete anchor relationships that have associated leads.',
  })
  @ApiParam({ name: 'id', description: 'Anchor relationship UUID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Anchor relationship deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Anchor relationship not found' })
  @ApiConflictResponse({ description: 'Anchor relationship has associated leads and cannot be deleted' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.anchorRelationshipsService.remove(id);
  }
}
