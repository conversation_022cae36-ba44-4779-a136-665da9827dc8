import { Module } from '@nestjs/common';
import { AnchorRelationshipsService } from './anchor-relationships.service';
import { AnchorRelationshipsController } from './anchor-relationships.controller';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * Module for Anchor Relationships functionality
 * Provides CRUD operations for anchor relationship management
 */
@Module({
  imports: [PrismaModule],
  providers: [AnchorRelationshipsService],
  controllers: [AnchorRelationshipsController],
  exports: [AnchorRelationshipsService], // Export service for use in other modules
})
export class AnchorRelationshipsModule {}
