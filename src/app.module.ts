import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaModule } from './prisma/prisma.module';
import { RegionsModule } from './regions/regions.module';
import { BranchesModule } from './branches/branches.module';
import { IsicSectorsModule } from './isic-sectors/isic-sectors.module';
import { CustomerCategoriesModule } from './customer-categories/customer-categories.module';
import { EmployersModule } from './employers/employers.module';
import { LeadsModule } from './leads/leads.module';
import { PermissionsModule } from './permissions/permissions.module';
import { RolesModule } from './roles/roles.module';
import { RolePermissionsModule } from './role-permissions/role-permissions.module';
import { UsersModule } from './users/users.module';
import { AnchorRelationshipsModule } from './anchor-relationships/anchor-relationships.module';
import { PurposesModule } from './purposes/purposes.module';
import { ActivitiesModule } from './activities/activities.module';
import { CustomersModule } from './customers/customers.module';
import { TargetsModule } from './targets/targets.module';

@Module({
  imports: [
    PrismaModule,
    RegionsModule,
    BranchesModule,
    IsicSectorsModule,
    CustomerCategoriesModule,
    EmployersModule,
    LeadsModule,
    PermissionsModule,
    RolesModule,
    RolePermissionsModule,
    UsersModule,
    AnchorRelationshipsModule,
    PurposesModule,
    ActivitiesModule,
    CustomersModule,
    TargetsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
