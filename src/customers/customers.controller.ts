import { Controller, Get, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CustomersService } from './customers.service';
import { CustomerResponseDto } from './dto/customer-response.dto';

/**
 * Controller handling customer-related HTTP endpoints
 * Provides customer data from leads table
 */
@ApiTags('Customers')
@Controller('customers')
export class CustomersController {
  constructor(private readonly customersService: CustomersService) {}

  /**
   * Gets all customers
   * GET /customers
   */
  @Get()
  @ApiOperation({
    summary: 'Get all customers',
    description:
      'Retrieves all customers from the leads table with client ID, account number, name, phone number, and branch information. Only includes leads that have an account number.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customers retrieved successfully',
    type: [CustomerResponseDto],
  })
  async getCustomers(): Promise<CustomerResponseDto[]> {
    return this.customersService.getCustomers();
  }
}
