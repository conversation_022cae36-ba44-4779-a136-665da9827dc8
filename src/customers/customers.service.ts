import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CustomerResponseDto } from './dto/customer-response.dto';

/**
 * Service handling customer-related business logic
 * Provides customer data from leads table
 */
@Injectable()
export class CustomersService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Gets all customers from leads table
   * Returns customer information including client_id, account_number, name, phone_number, and branch
   * Only includes leads that have an account number
   */
  async getCustomers(): Promise<CustomerResponseDto[]> {
    const leads = await this.prisma.lead.findMany({
      where: {
        account_number: {
          not: null,
        },
      },
      select: {
        client_id: true,
        account_number: true,
        customer_name: true,
        phone_number: true,
        branch: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        customer_name: 'asc',
      },
    });

    return leads.map((lead) => ({
      client_id: lead.client_id,
      account_number: lead.account_number,
      name: lead.customer_name || 'Unknown Customer',
      phone_number: lead.phone_number,
      branch: lead.branch
        ? {
            id: lead.branch.id,
            name: lead.branch.name,
          }
        : null,
    }));
  }
}
