import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for customer response
 * Returns customer information from leads table
 */
export class CustomerResponseDto {
  @ApiPropertyOptional({
    description: 'Client ID of the customer',
    example: 'CL865972',
    nullable: true,
  })
  client_id: string | null;

  @ApiPropertyOptional({
    description: 'Account number of the customer',
    example: 'ACC123456789',
    nullable: true,
  })
  account_number: string | null;

  @ApiProperty({
    description: 'Name of the customer',
    example: '<PERSON>',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Phone number of the customer',
    example: '+************',
    nullable: true,
  })
  phone_number: string | null;

  @ApiPropertyOptional({
    description: 'Branch information',
    nullable: true,
  })
  branch: {
    id: string;
    name: string;
  } | null;
}
