import {
  Injectable,
  On<PERSON><PERSON>ule<PERSON><PERSON><PERSON>,
  OnM<PERSON>ule<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

/**
 * Service for managing database connections using Prisma
 * Handles connection lifecycle and provides query logging
 */
@Injectable()
export class PrismaService
  extends PrismaClient
  implements OnModuleInit, OnModuleDestroy
{
  private readonly logger = new Logger(PrismaService.name);

  constructor() {
    super({
      log: ['info', 'warn', 'error'],
    });
  }

  /**
   * Connect to the database when the module initializes
   */
  async onModuleInit() {
    this.logger.log('Connecting to database...');
    try {
      await this.$connect();
      this.logger.log('Database connection established');
    } catch (error) {
      this.logger.error('Failed to connect to database:', error.message);
      this.logger.warn('Application will start without database connection');
      // Don't throw error to allow app to start for testing API structure
    }
  }

  /**
   * Disconnect from the database when the module is destroyed
   */
  async onModuleD<PERSON>roy() {
    this.logger.log('Disconnecting from database...');
    await this.$disconnect();
    this.logger.log('Database connection closed');
  }
}
