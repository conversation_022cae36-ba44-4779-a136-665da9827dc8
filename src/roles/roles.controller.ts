import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
} from '@nestjs/swagger';
import { RolesService } from './roles.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { RoleResponseDto } from './dto/role-response.dto';
import { AssignPermissionsDto, RemovePermissionsDto } from './dto/assign-permissions.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

/**
 * Roles Controller
 * 
 * This controller handles all HTTP requests related to role management.
 * It provides comprehensive CRUD operations and role-permission management with:
 * - Input validation using DTOs
 * - Comprehensive error responses
 * - Pagination support for list operations
 * - Search functionality
 * - Role-permission relationship management
 * - Detailed API documentation
 * 
 * @controller roles
 */
@ApiTags('roles')
@Controller('roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  /**
   * Creates a new role with optional permission assignments
   * POST /roles
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new role',
    description: 'Creates a new role with the provided name, optional description, and optional permission assignments. Role names must be unique (case-insensitive).'
  })
  @ApiResponse({
    status: 201,
    description: 'Role created successfully',
    type: RoleResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data, invalid permission IDs, or database connection failed' })
  @ApiConflictResponse({ description: 'Role name already exists' })
  async create(
    @Body(ValidationPipe) createRoleDto: CreateRoleDto
  ): Promise<RoleResponseDto> {
    return this.rolesService.create(createRoleDto);
  }

  /**
   * Retrieves all roles with pagination and search
   * GET /roles
   */
  @Get()
  @ApiOperation({
    summary: 'Get all roles',
    description: 'Retrieves a paginated list of roles with optional search functionality. Search works on both name and description fields. Includes user and permission counts.'
  })
  @ApiResponse({
    status: 200,
    description: 'Roles retrieved successfully',
    type: PaginatedResponseDto<RoleResponseDto>,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page (default: 10, max: 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term to filter roles by name or description',
    example: 'admin',
  })
  @ApiBadRequestResponse({ description: 'Invalid query parameters or database connection failed' })
  async findAll(
    @Query(ValidationPipe) paginationDto: PaginationDto
  ): Promise<PaginatedResponseDto<RoleResponseDto>> {
    return this.rolesService.findAll(paginationDto);
  }

  /**
   * Retrieves a single role by ID with detailed information
   * GET /roles/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get role by ID',
    description: 'Retrieves a specific role by its UUID, including detailed information about assigned permissions, user count, and permission count.'
  })
  @ApiParam({
    name: 'id',
    description: 'Role UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Role retrieved successfully',
    type: RoleResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Role not found' })
  @ApiBadRequestResponse({ description: 'Invalid UUID format or database connection failed' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<RoleResponseDto> {
    return this.rolesService.findOne(id);
  }

  /**
   * Updates an existing role with optional permission reassignment
   * PATCH /roles/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update role',
    description: 'Updates an existing role. Only provided fields will be updated. If permissionIds are provided, they will replace all existing permissions. Role name must remain unique if changed.'
  })
  @ApiParam({
    name: 'id',
    description: 'Role UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Role updated successfully',
    type: RoleResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Role not found' })
  @ApiBadRequestResponse({ description: 'Invalid input data, invalid permission IDs, or database connection failed' })
  @ApiConflictResponse({ description: 'Role name already exists' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateRoleDto: UpdateRoleDto
  ): Promise<RoleResponseDto> {
    return this.rolesService.update(id, updateRoleDto);
  }

  /**
   * Deletes a role
   * DELETE /roles/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete role',
    description: 'Deletes a role by ID. Cannot delete roles that are assigned to users - reassign or remove all users first. All role-permission relationships are automatically removed.'
  })
  @ApiParam({
    name: 'id',
    description: 'Role UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Role deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Role not found' })
  @ApiBadRequestResponse({ description: 'Invalid UUID format or database connection failed' })
  @ApiConflictResponse({ description: 'Role is assigned to users and cannot be deleted' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.rolesService.remove(id);
  }

  /**
   * Assigns additional permissions to a role
   * POST /roles/:id/permissions
   */
  @Post(':id/permissions')
  @ApiOperation({
    summary: 'Assign permissions to role',
    description: 'Assigns additional permissions to an existing role. Permissions that are already assigned will cause a conflict error.'
  })
  @ApiParam({
    name: 'id',
    description: 'Role UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Permissions assigned successfully',
    type: RoleResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Role not found or invalid permission IDs' })
  @ApiBadRequestResponse({ description: 'Invalid input data, invalid permission IDs, or database connection failed' })
  @ApiConflictResponse({ description: 'Some permissions are already assigned to the role' })
  async assignPermissions(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) assignPermissionsDto: AssignPermissionsDto
  ): Promise<RoleResponseDto> {
    return this.rolesService.assignPermissions(id, assignPermissionsDto);
  }

  /**
   * Removes permissions from a role
   * DELETE /roles/:id/permissions
   */
  @Delete(':id/permissions')
  @ApiOperation({
    summary: 'Remove permissions from role',
    description: 'Removes specified permissions from a role. Permissions that are not assigned will cause a not found error.'
  })
  @ApiParam({
    name: 'id',
    description: 'Role UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Permissions removed successfully',
    type: RoleResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Role not found or permissions not assigned to role' })
  @ApiBadRequestResponse({ description: 'Invalid input data or database connection failed' })
  async removePermissions(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) removePermissionsDto: RemovePermissionsDto
  ): Promise<RoleResponseDto> {
    return this.rolesService.removePermissions(id, removePermissionsDto);
  }

  /**
   * Retrieves all users assigned to a specific role
   * GET /roles/:id/users
   */
  @Get(':id/users')
  @ApiOperation({
    summary: 'Get users with role',
    description: 'Retrieves a paginated list of users that have the specified role assigned.'
  })
  @ApiParam({
    name: 'id',
    description: 'Role UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page (default: 10, max: 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term to filter users by name or email',
    example: 'john',
  })
  @ApiResponse({
    status: 200,
    description: 'Users retrieved successfully',
  })
  @ApiNotFoundResponse({ description: 'Role not found' })
  @ApiBadRequestResponse({ description: 'Invalid query parameters or database connection failed' })
  async getRoleUsers(
    @Param('id', ParseUUIDPipe) id: string,
    @Query(ValidationPipe) paginationDto: PaginationDto
  ) {
    return this.rolesService.getRoleUsers(id, paginationDto);
  }
}
