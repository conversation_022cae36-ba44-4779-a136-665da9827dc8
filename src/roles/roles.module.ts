import { Module } from '@nestjs/common';
import { RolesService } from './roles.service';
import { RolesController } from './roles.controller';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * Roles Module
 * 
 * This module handles all role-related functionality including:
 * - CRUD operations for roles
 * - Role-permission relationship management
 * - Role validation and assignment
 * - Integration with user management
 * 
 * @module RolesModule
 */
@Module({
  imports: [PrismaModule],
  providers: [RolesService],
  controllers: [RolesController],
  exports: [RolesService], // Export service for use in other modules
})
export class RolesModule {}
