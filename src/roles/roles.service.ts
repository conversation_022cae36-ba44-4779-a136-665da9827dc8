import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { RoleResponseDto } from './dto/role-response.dto';
import { AssignPermissionsDto, RemovePermissionsDto } from './dto/assign-permissions.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { Prisma } from '@prisma/client';

/**
 * Service responsible for handling all role-related business logic
 * 
 * This service implements comprehensive CRUD operations for roles with:
 * - Optimized database queries using Prisma
 * - Proper error handling and validation
 * - Role-permission relationship management
 * - User and permission count aggregation
 * - Search and pagination functionality
 * - Transactional operations for data consistency
 * 
 * @class RolesService
 */
@Injectable()
export class RolesService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new role with optional permission assignments
   * 
   * @param createRoleDto - Data for creating the role including optional permission IDs
   * @returns Promise<RoleResponseDto> - The created role with counts and permissions
   * @throws ConflictException if role name already exists
   * @throws BadRequestException if permission IDs are invalid or database operation fails
   */
  async create(createRoleDto: CreateRoleDto): Promise<RoleResponseDto> {
    try {
      // Check if role with same name already exists (case-insensitive)
      const existingRole = await this.prisma.role.findFirst({
        where: {
          name: {
            equals: createRoleDto.name,
            mode: 'insensitive',
          },
        },
      });

      if (existingRole) {
        throw new ConflictException(`Role with name '${createRoleDto.name}' already exists`);
      }

      // If permission IDs are provided, validate they exist
      if (createRoleDto.permissionIds && createRoleDto.permissionIds.length > 0) {
        const existingPermissions = await this.prisma.permission.findMany({
          where: {
            id: {
              in: createRoleDto.permissionIds,
            },
          },
          select: { id: true },
        });

        const foundPermissionIds = existingPermissions.map(p => p.id);
        const invalidPermissionIds = createRoleDto.permissionIds.filter(
          id => !foundPermissionIds.includes(id)
        );

        if (invalidPermissionIds.length > 0) {
          throw new BadRequestException(
            `Invalid permission IDs: ${invalidPermissionIds.join(', ')}`
          );
        }
      }

      // Create role and assign permissions in a transaction
      const result = await this.prisma.$transaction(async (tx) => {
        // Create the role
        const role = await tx.role.create({
          data: {
            name: createRoleDto.name,
            description: createRoleDto.description,
          },
        });

        // Assign permissions if provided
        if (createRoleDto.permissionIds && createRoleDto.permissionIds.length > 0) {
          await tx.rolePermission.createMany({
            data: createRoleDto.permissionIds.map(permissionId => ({
              role_id: role.id,
              permission_id: permissionId,
            })),
          });
        }

        // Fetch the complete role with permissions and counts
        return await tx.role.findUnique({
          where: { id: role.id },
          include: {
            role_permissions: {
              include: {
                permission: {
                  select: {
                    id: true,
                    name: true,
                    description: true,
                  },
                },
              },
            },
            _count: {
              select: {
                users: true,
                role_permissions: true,
              },
            },
          },
        });
      });

      if (!result) {
        throw new BadRequestException('Failed to create role - transaction returned null');
      }

      return {
        id: result.id,
        name: result.name,
        description: result.description,
        userCount: (result as any)._count.users,
        permissionCount: (result as any)._count.role_permissions,
        permissions: result.role_permissions.map(rp => ({
          id: rp.permission.id,
          name: rp.permission.name,
          description: rp.permission.description,
        })),
      };
    } catch (error) {
      if (error instanceof ConflictException || error instanceof BadRequestException) {
        throw error;
      }
      
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }
      
      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      console.error('Error creating role:', error);
      throw new BadRequestException('Failed to create role');
    }
  }

  /**
   * Retrieves all roles with pagination and optional search
   * 
   * @param paginationDto - Pagination and search parameters
   * @returns Promise<PaginatedResponseDto<RoleResponseDto>> - Paginated list of roles
   * @throws BadRequestException if database operation fails
   */
  async findAll(paginationDto: PaginationDto): Promise<PaginatedResponseDto<RoleResponseDto>> {
    try {
      const { page = 1, limit = 10, search } = paginationDto;
      const skip = (page - 1) * limit;

      // Build where clause for search functionality
      const whereClause: Prisma.RoleWhereInput = search
        ? {
            OR: [
              {
                name: {
                  contains: search,
                  mode: 'insensitive', // Case-insensitive search
                },
              },
              {
                description: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
            ],
          }
        : {};

      // Execute both count and data queries in parallel for better performance
      const [roles, total] = await Promise.all([
        this.prisma.role.findMany({
          where: whereClause,
          skip,
          take: limit,
          include: {
            _count: {
              select: {
                users: true,
                role_permissions: true,
              },
            },
          },
          orderBy: {
            name: 'asc', // Sort by name alphabetically
          },
        }),
        this.prisma.role.count({
          where: whereClause,
        }),
      ]);

      // Transform data to response DTOs
      const data = roles.map((role) => ({
        id: role.id,
        name: role.name,
        description: role.description,
        userCount: (role as any)._count.users,
        permissionCount: (role as any)._count.role_permissions,
      }));

      const totalPages = Math.ceil(total / limit);

      return {
        data,
        meta: {
          total,
          page,
          limit,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
        },
      };
    } catch (error) {
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }
      
      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      console.error('Error fetching roles:', error);
      throw new BadRequestException('Failed to fetch roles');
    }
  }

  /**
   * Retrieves a single role by ID with detailed information
   *
   * @param id - The UUID of the role
   * @returns Promise<RoleResponseDto> - The role data with permissions and counts
   * @throws NotFoundException if role doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async findOne(id: string): Promise<RoleResponseDto> {
    try {
      const role = await this.prisma.role.findUnique({
        where: { id },
        include: {
          role_permissions: {
            include: {
              permission: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
            },
          },
          _count: {
            select: {
              users: true,
              role_permissions: true,
            },
          },
        },
      });

      if (!role) {
        throw new NotFoundException(`Role with ID '${id}' not found`);
      }

      return {
        id: role.id,
        name: role.name,
        description: role.description,
        userCount: (role as any)._count.users,
        permissionCount: (role as any)._count.role_permissions,
        permissions: role.role_permissions.map(rp => ({
          id: rp.permission.id,
          name: rp.permission.name,
          description: rp.permission.description,
        })),
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error fetching role:', error);
      throw new BadRequestException('Failed to fetch role');
    }
  }

  /**
   * Updates an existing role with optional permission reassignment
   *
   * @param id - The UUID of the role to update
   * @param updateRoleDto - Data for updating the role including optional permission IDs
   * @returns Promise<RoleResponseDto> - The updated role with permissions and counts
   * @throws NotFoundException if role doesn't exist
   * @throws ConflictException if new name conflicts with existing role
   * @throws BadRequestException if permission IDs are invalid or database operation fails
   */
  async update(id: string, updateRoleDto: UpdateRoleDto): Promise<RoleResponseDto> {
    try {
      // Check if role exists
      const existingRole = await this.prisma.role.findUnique({
        where: { id },
      });

      if (!existingRole) {
        throw new NotFoundException(`Role with ID '${id}' not found`);
      }

      // If name is being updated, check for conflicts
      if (updateRoleDto.name && updateRoleDto.name !== existingRole.name) {
        const conflictingRole = await this.prisma.role.findFirst({
          where: {
            name: {
              equals: updateRoleDto.name,
              mode: 'insensitive',
            },
            id: {
              not: id, // Exclude current role from conflict check
            },
          },
        });

        if (conflictingRole) {
          throw new ConflictException(`Role with name '${updateRoleDto.name}' already exists`);
        }
      }

      // If permission IDs are provided, validate they exist
      if (updateRoleDto.permissionIds && updateRoleDto.permissionIds.length > 0) {
        const existingPermissions = await this.prisma.permission.findMany({
          where: {
            id: {
              in: updateRoleDto.permissionIds,
            },
          },
          select: { id: true },
        });

        const foundPermissionIds = existingPermissions.map(p => p.id);
        const invalidPermissionIds = updateRoleDto.permissionIds.filter(
          id => !foundPermissionIds.includes(id)
        );

        if (invalidPermissionIds.length > 0) {
          throw new BadRequestException(
            `Invalid permission IDs: ${invalidPermissionIds.join(', ')}`
          );
        }
      }

      // Update role and reassign permissions in a transaction
      const result = await this.prisma.$transaction(async (tx) => {
        // Update the role basic information
        const updatedRole = await tx.role.update({
          where: { id },
          data: {
            name: updateRoleDto.name,
            description: updateRoleDto.description,
          },
        });

        // If permission IDs are provided, replace all existing permissions
        if (updateRoleDto.permissionIds !== undefined) {
          // Remove all existing role-permission relationships
          await tx.rolePermission.deleteMany({
            where: { role_id: id },
          });

          // Add new role-permission relationships if any permissions provided
          if (updateRoleDto.permissionIds.length > 0) {
            await tx.rolePermission.createMany({
              data: updateRoleDto.permissionIds.map(permissionId => ({
                role_id: id,
                permission_id: permissionId,
              })),
            });
          }
        }

        // Fetch the complete updated role with permissions and counts
        return await tx.role.findUnique({
          where: { id },
          include: {
            role_permissions: {
              include: {
                permission: {
                  select: {
                    id: true,
                    name: true,
                    description: true,
                  },
                },
              },
            },
            _count: {
              select: {
                users: true,
                role_permissions: true,
              },
            },
          },
        });
      });

      if (!result) {
        throw new BadRequestException('Failed to update role - transaction returned null');
      }

      return {
        id: result.id,
        name: result.name,
        description: result.description,
        userCount: (result as any)._count.users,
        permissionCount: (result as any)._count.role_permissions,
        permissions: result.role_permissions.map(rp => ({
          id: rp.permission.id,
          name: rp.permission.name,
          description: rp.permission.description,
        })),
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof BadRequestException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error updating role:', error);
      throw new BadRequestException('Failed to update role');
    }
  }

  /**
   * Deletes a role by ID
   *
   * @param id - The UUID of the role to delete
   * @returns Promise<void>
   * @throws NotFoundException if role doesn't exist
   * @throws ConflictException if role is assigned to users
   * @throws BadRequestException if database operation fails
   */
  async remove(id: string): Promise<void> {
    try {
      // Check if role exists and has user assignments
      const role = await this.prisma.role.findUnique({
        where: { id },
        include: {
          _count: {
            select: { users: true },
          },
        },
      });

      if (!role) {
        throw new NotFoundException(`Role with ID '${id}' not found`);
      }

      // Prevent deletion if role is assigned to users
      if ((role as any)._count.users > 0) {
        throw new ConflictException(
          `Cannot delete role '${role.name}' because it is assigned to ${(role as any)._count.users} user(s). Please reassign or remove these users first.`
        );
      }

      // Delete role and its permissions in a transaction
      await this.prisma.$transaction(async (tx) => {
        // Delete all role-permission relationships first
        await tx.rolePermission.deleteMany({
          where: { role_id: id },
        });

        // Delete the role
        await tx.role.delete({
          where: { id },
        });
      });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error deleting role:', error);
      throw new BadRequestException('Failed to delete role');
    }
  }

  /**
   * Assigns additional permissions to a role
   *
   * @param roleId - The UUID of the role
   * @param assignPermissionsDto - Permission IDs to assign
   * @returns Promise<RoleResponseDto> - The updated role with permissions
   * @throws NotFoundException if role or permissions don't exist
   * @throws ConflictException if permissions are already assigned
   * @throws BadRequestException if database operation fails
   */
  async assignPermissions(roleId: string, assignPermissionsDto: AssignPermissionsDto): Promise<RoleResponseDto> {
    try {
      // Verify role exists
      const role = await this.prisma.role.findUnique({
        where: { id: roleId },
      });

      if (!role) {
        throw new NotFoundException(`Role with ID '${roleId}' not found`);
      }

      // Validate all permission IDs exist
      const existingPermissions = await this.prisma.permission.findMany({
        where: {
          id: {
            in: assignPermissionsDto.permissionIds,
          },
        },
        select: { id: true },
      });

      const foundPermissionIds = existingPermissions.map(p => p.id);
      const invalidPermissionIds = assignPermissionsDto.permissionIds.filter(
        id => !foundPermissionIds.includes(id)
      );

      if (invalidPermissionIds.length > 0) {
        throw new BadRequestException(
          `Invalid permission IDs: ${invalidPermissionIds.join(', ')}`
        );
      }

      // Check for existing assignments
      const existingAssignments = await this.prisma.rolePermission.findMany({
        where: {
          role_id: roleId,
          permission_id: {
            in: assignPermissionsDto.permissionIds,
          },
        },
        select: { permission_id: true },
      });

      const alreadyAssignedIds = existingAssignments.map(rp => rp.permission_id);
      if (alreadyAssignedIds.length > 0) {
        throw new ConflictException(
          `Permissions already assigned to role: ${alreadyAssignedIds.join(', ')}`
        );
      }

      // Assign permissions in a transaction
      const result = await this.prisma.$transaction(async (tx) => {
        // Create new role-permission relationships
        await tx.rolePermission.createMany({
          data: assignPermissionsDto.permissionIds.map(permissionId => ({
            role_id: roleId,
            permission_id: permissionId,
          })),
        });

        // Fetch the updated role with permissions and counts
        return await tx.role.findUnique({
          where: { id: roleId },
          include: {
            role_permissions: {
              include: {
                permission: {
                  select: {
                    id: true,
                    name: true,
                    description: true,
                  },
                },
              },
            },
            _count: {
              select: {
                users: true,
                role_permissions: true,
              },
            },
          },
        });
      });

      if (!result) {
        throw new BadRequestException('Failed to assign permissions - transaction returned null');
      }

      return {
        id: result.id,
        name: result.name,
        description: result.description,
        userCount: (result as any)._count.users,
        permissionCount: (result as any)._count.role_permissions,
        permissions: result.role_permissions.map(rp => ({
          id: rp.permission.id,
          name: rp.permission.name,
          description: rp.permission.description,
        })),
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof BadRequestException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error assigning permissions to role:', error);
      throw new BadRequestException('Failed to assign permissions to role');
    }
  }

  /**
   * Removes permissions from a role
   *
   * @param roleId - The UUID of the role
   * @param removePermissionsDto - Permission IDs to remove
   * @returns Promise<RoleResponseDto> - The updated role with permissions
   * @throws NotFoundException if role doesn't exist or permissions not assigned
   * @throws BadRequestException if database operation fails
   */
  async removePermissions(roleId: string, removePermissionsDto: RemovePermissionsDto): Promise<RoleResponseDto> {
    try {
      // Verify role exists
      const role = await this.prisma.role.findUnique({
        where: { id: roleId },
      });

      if (!role) {
        throw new NotFoundException(`Role with ID '${roleId}' not found`);
      }

      // Check which permissions are actually assigned to the role
      const existingAssignments = await this.prisma.rolePermission.findMany({
        where: {
          role_id: roleId,
          permission_id: {
            in: removePermissionsDto.permissionIds,
          },
        },
        select: { permission_id: true },
      });

      const assignedPermissionIds = existingAssignments.map(rp => rp.permission_id);
      const notAssignedIds = removePermissionsDto.permissionIds.filter(
        id => !assignedPermissionIds.includes(id)
      );

      if (notAssignedIds.length > 0) {
        throw new NotFoundException(
          `Permissions not assigned to role: ${notAssignedIds.join(', ')}`
        );
      }

      // Remove permissions in a transaction
      const result = await this.prisma.$transaction(async (tx) => {
        // Delete role-permission relationships
        await tx.rolePermission.deleteMany({
          where: {
            role_id: roleId,
            permission_id: {
              in: removePermissionsDto.permissionIds,
            },
          },
        });

        // Fetch the updated role with permissions and counts
        return await tx.role.findUnique({
          where: { id: roleId },
          include: {
            role_permissions: {
              include: {
                permission: {
                  select: {
                    id: true,
                    name: true,
                    description: true,
                  },
                },
              },
            },
            _count: {
              select: {
                users: true,
                role_permissions: true,
              },
            },
          },
        });
      });

      if (!result) {
        throw new BadRequestException('Failed to remove permissions - transaction returned null');
      }

      return {
        id: result.id,
        name: result.name,
        description: result.description,
        userCount: (result as any)._count.users,
        permissionCount: (result as any)._count.role_permissions,
        permissions: result.role_permissions.map(rp => ({
          id: rp.permission.id,
          name: rp.permission.name,
          description: rp.permission.description,
        })),
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error removing permissions from role:', error);
      throw new BadRequestException('Failed to remove permissions from role');
    }
  }

  /**
   * Retrieves all users assigned to a specific role
   *
   * @param roleId - The UUID of the role
   * @param paginationDto - Pagination parameters
   * @returns Promise<PaginatedResponseDto<any>> - Paginated list of users with this role
   * @throws NotFoundException if role doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async getRoleUsers(roleId: string, paginationDto: PaginationDto) {
    try {
      // Verify role exists
      const role = await this.prisma.role.findUnique({
        where: { id: roleId },
      });

      if (!role) {
        throw new NotFoundException(`Role with ID '${roleId}' not found`);
      }

      const { page = 1, limit = 10, search } = paginationDto;
      const skip = (page - 1) * limit;

      const whereClause: Prisma.UserWhereInput = {
        role_id: roleId,
        ...(search && {
          OR: [
            {
              name: {
                contains: search,
                mode: 'insensitive',
              },
            },
            {
              email: {
                contains: search,
                mode: 'insensitive',
              },
            },
          ],
        }),
      };

      const [users, total] = await Promise.all([
        this.prisma.user.findMany({
          where: whereClause,
          skip,
          take: limit,
          select: {
            id: true,
            name: true,
            email: true,
          },
          orderBy: {
            name: 'asc',
          },
        }),
        this.prisma.user.count({
          where: whereClause,
        }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        data: users,
        meta: {
          total,
          page,
          limit,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
        },
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error fetching role users:', error);
      throw new BadRequestException('Failed to fetch role users');
    }
  }
}
