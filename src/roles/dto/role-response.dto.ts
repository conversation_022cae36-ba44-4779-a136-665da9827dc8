import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for permission information in role responses
 * 
 * @class PermissionInfoDto
 */
export class PermissionInfoDto {
  @ApiProperty({
    description: 'Custom permission identifier',
    example: 'users.create',
  })
  id: string;

  @ApiProperty({
    description: 'Human-readable name of the permission',
    example: 'Create Users',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Optional description of the permission',
    example: 'Allows creating new user accounts',
    nullable: true,
  })
  description: string | null;
}

/**
 * Data Transfer Object for role response
 * 
 * Defines the structure of role data returned by the API.
 * This ensures consistent response format across all role endpoints.
 * 
 * @class RoleResponseDto
 */
export class RoleResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the role',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the role',
    example: 'Manager',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Optional description explaining the role and its responsibilities',
    example: 'Manager role with access to team management and reporting features',
    nullable: true,
  })
  description: string | null;

  @ApiPropertyOptional({
    description: 'Number of users assigned to this role',
    example: 5,
  })
  userCount?: number;

  @ApiPropertyOptional({
    description: 'Number of permissions assigned to this role',
    example: 12,
  })
  permissionCount?: number;

  @ApiPropertyOptional({
    description: 'List of permissions assigned to this role (only included in detailed responses)',
    type: [PermissionInfoDto],
  })
  permissions?: PermissionInfoDto[];
}
