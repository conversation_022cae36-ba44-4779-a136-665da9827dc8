import { <PERSON><PERSON>otE<PERSON>y, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for creating a new role
 *
 * This DTO validates input data for role creation and provides
 * comprehensive API documentation for Swagger/OpenAPI.
 *
 * @class CreateRoleDto
 */
export class CreateRoleDto {
  @ApiProperty({
    description: 'The name of the role (e.g., "Admin", "Manager", "User")',
    example: 'Manager',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Role name is required' })
  @IsString({ message: 'Role name must be a string' })
  @MaxLength(255, { message: 'Role name cannot exceed 255 characters' })
  name: string;

  @ApiPropertyOptional({
    description: 'Optional description explaining the role and its responsibilities',
    example: 'Manager role with access to team management and reporting features',
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Role description must be a string' })
  @MaxLength(500, { message: 'Role description cannot exceed 500 characters' })
  description?: string;

  @ApiPropertyOptional({
    description: 'Array of custom permission IDs to assign to this role',
    example: ['users.create', 'users.read', 'reports.generate'],
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Permission IDs must be an array' })
  @IsString({ each: true, message: 'Each permission ID must be a string' })
  @Matches(/^[a-z][a-z0-9]*(\.[a-z][a-z0-9]*)*$/, {
    each: true,
    message: 'Each permission ID must be lowercase, start with a letter, and use dots for hierarchy (e.g., "users.create")'
  })
  permissionIds?: string[];
}
