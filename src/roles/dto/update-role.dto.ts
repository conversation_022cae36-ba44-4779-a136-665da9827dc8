import { PartialType } from '@nestjs/mapped-types';
import { CreateRoleDto } from './create-role.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for updating an existing role
 * 
 * Extends CreateRoleDto but makes all fields optional.
 * This allows for partial updates where only specific fields need to be modified.
 * 
 * @class UpdateRoleDto
 */
export class UpdateRoleDto extends PartialType(CreateRoleDto) {
  @ApiPropertyOptional({
    description: 'The name of the role',
    example: 'Senior Manager',
    maxLength: 255,
  })
  name?: string;

  @ApiPropertyOptional({
    description: 'Optional description explaining the role and its responsibilities',
    example: 'Senior manager role with additional administrative privileges',
    maxLength: 500,
  })
  description?: string;

  @ApiPropertyOptional({
    description: 'Array of custom permission IDs to assign to this role (replaces existing permissions)',
    example: ['users.create', 'users.update', 'reports.read'],
    type: [String],
  })
  permissionIds?: string[];
}
