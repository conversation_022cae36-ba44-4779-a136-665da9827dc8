import { Module } from '@nestjs/common';
import { IsicSectorsService } from './isic-sectors.service';
import { IsicSectorsController } from './isic-sectors.controller';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * Module for ISIC Sectors functionality
 * Provides CRUD operations for ISIC sector management
 */
@Module({
  imports: [PrismaModule],
  providers: [IsicSectorsService],
  controllers: [IsicSectorsController],
  exports: [IsicSectorsService], // Export service for use in other modules
})
export class IsicSectorsModule {}
