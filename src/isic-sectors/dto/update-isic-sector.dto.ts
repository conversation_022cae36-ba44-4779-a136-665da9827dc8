import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * Data Transfer Object for updating an existing ISIC sector
 * All fields are optional for partial updates
 */
export class UpdateIsicSectorDto {
  @ApiPropertyOptional({
    description: 'The ISIC code for the sector',
    example: 'A01',
    maxLength: 10,
  })
  @IsOptional()
  @IsString({ message: 'ISIC code must be a string' })
  @MaxLength(10, { message: 'ISIC code cannot exceed 10 characters' })
  code?: string;

  @ApiPropertyOptional({
    description: 'The name of the ISIC sector',
    example: 'Updated Agriculture, forestry and fishing',
    maxLength: 255,
  })
  @IsOptional()
  @IsString({ message: 'Sector name must be a string' })
  @MaxLength(255, { message: 'Sector name cannot exceed 255 characters' })
  name?: string;
}
