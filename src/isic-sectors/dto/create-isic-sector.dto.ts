import { IsNotEmpty, <PERSON>S<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for creating a new ISIC sector
 * Validates input data and provides API documentation
 */
export class CreateIsicSectorDto {
  @ApiPropertyOptional({
    description: 'The ISIC code for the sector',
    example: 'A01',
    maxLength: 10,
  })
  @IsOptional()
  @IsString({ message: 'ISIC code must be a string' })
  @MaxLength(10, { message: 'ISIC code cannot exceed 10 characters' })
  code?: string;

  @ApiProperty({
    description: 'The name of the ISIC sector',
    example: 'Agriculture, forestry and fishing',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Sector name is required' })
  @IsString({ message: 'Sector name must be a string' })
  @MaxLength(255, { message: 'Sector name cannot exceed 255 characters' })
  name: string;
}
