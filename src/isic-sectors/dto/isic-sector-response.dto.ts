import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for ISIC sector response
 * Defines the structure of ISIC sector data returned by the API
 */
export class IsicSectorResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the ISIC sector',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'The ISIC code for the sector (can be empty)',
    example: 'A01',
    nullable: true,
  })
  code: string | null;

  @ApiProperty({
    description: 'The name of the ISIC sector',
    example: 'Agriculture, forestry and fishing',
  })
  name: string;

  @ApiProperty({
    description: 'Date when the ISIC sector was added',
    example: '2024-01-15T10:30:00.000Z',
  })
  addedOnDate: Date | null;

  @ApiProperty({
    description: 'User who added the ISIC sector (empty for now)',
    example: '',
  })
  addedBy: string;
}
