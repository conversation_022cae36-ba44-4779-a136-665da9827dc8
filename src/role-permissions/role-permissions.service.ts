import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { BulkRolePermissionDto, CopyPermissionsDto } from './dto/bulk-operations.dto';
import { RolePermissionResponseDto, RolePermissionAnalyticsDto } from './dto/role-permission-response.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { Prisma } from '@prisma/client';

/**
 * Service responsible for handling role-permission relationship operations
 * 
 * This service provides specialized functionality for managing the many-to-many
 * relationship between roles and permissions, including:
 * - Bulk operations for efficiency
 * - Advanced querying and filtering
 * - Analytics and reporting
 * - Permission copying between roles
 * - Relationship validation and integrity
 * 
 * @class RolePermissionsService
 */
@Injectable()
export class RolePermissionsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Retrieves all role-permission relationships with pagination and filtering
   * 
   * @param paginationDto - Pagination and search parameters
   * @returns Promise<PaginatedResponseDto<RolePermissionResponseDto>> - Paginated relationships
   * @throws BadRequestException if database operation fails
   */
  async findAll(paginationDto: PaginationDto): Promise<PaginatedResponseDto<RolePermissionResponseDto>> {
    try {
      const { page = 1, limit = 10, search } = paginationDto;
      const skip = (page - 1) * limit;

      // Build where clause for search functionality
      const whereClause: Prisma.RolePermissionWhereInput = search
        ? {
            OR: [
              {
                role: {
                  name: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
              },
              {
                permission: {
                  name: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
              },
              {
                role: {
                  description: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
              },
              {
                permission: {
                  description: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
              },
            ],
          }
        : {};

      // Execute both count and data queries in parallel
      const [rolePermissions, total] = await Promise.all([
        this.prisma.rolePermission.findMany({
          where: whereClause,
          skip,
          take: limit,
          include: {
            role: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
            permission: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
          },
          orderBy: [
            { role: { name: 'asc' } },
            { permission: { name: 'asc' } },
          ],
        }),
        this.prisma.rolePermission.count({
          where: whereClause,
        }),
      ]);

      // Transform data to response DTOs
      const data = rolePermissions.map((rp) => ({
        role: {
          id: rp.role.id,
          name: rp.role.name,
          description: rp.role.description,
        },
        permission: {
          id: rp.permission.id,
          name: rp.permission.name,
          description: rp.permission.description,
        },
        assignedAt: rp.created_at,
      }));

      const totalPages = Math.ceil(total / limit);

      return {
        data,
        meta: {
          total,
          page,
          limit,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
        },
      };
    } catch (error) {
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }
      
      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      console.error('Error fetching role-permission relationships:', error);
      throw new BadRequestException('Failed to fetch role-permission relationships');
    }
  }

  /**
   * Performs bulk assignment of role-permission relationships
   * 
   * @param bulkRolePermissionDto - Bulk assignment data
   * @returns Promise<{ created: number, skipped: number }> - Operation results
   * @throws BadRequestException if invalid IDs or database operation fails
   * @throws NotFoundException if roles or permissions don't exist
   */
  async bulkAssign(bulkRolePermissionDto: BulkRolePermissionDto): Promise<{ created: number; skipped: number }> {
    try {
      const { assignments } = bulkRolePermissionDto;

      // Extract unique role and permission IDs for validation
      const roleIds = [...new Set(assignments.map(a => a.roleId))];
      const permissionIds = [...new Set(assignments.map(a => a.permissionId))];

      // Validate all roles exist
      const existingRoles = await this.prisma.role.findMany({
        where: { id: { in: roleIds } },
        select: { id: true },
      });

      const foundRoleIds = existingRoles.map(r => r.id);
      const invalidRoleIds = roleIds.filter(id => !foundRoleIds.includes(id));

      if (invalidRoleIds.length > 0) {
        throw new NotFoundException(`Invalid role IDs: ${invalidRoleIds.join(', ')}`);
      }

      // Validate all permissions exist
      const existingPermissions = await this.prisma.permission.findMany({
        where: { id: { in: permissionIds } },
        select: { id: true },
      });

      const foundPermissionIds = existingPermissions.map(p => p.id);
      const invalidPermissionIds = permissionIds.filter(id => !foundPermissionIds.includes(id));

      if (invalidPermissionIds.length > 0) {
        throw new NotFoundException(`Invalid permission IDs: ${invalidPermissionIds.join(', ')}`);
      }

      // Check for existing assignments
      const existingAssignments = await this.prisma.rolePermission.findMany({
        where: {
          OR: assignments.map(a => ({
            role_id: a.roleId,
            permission_id: a.permissionId,
          })),
        },
        select: {
          role_id: true,
          permission_id: true,
        },
      });

      const existingKeys = new Set(
        existingAssignments.map(ea => `${ea.role_id}:${ea.permission_id}`)
      );

      // Filter out existing assignments
      const newAssignments = assignments.filter(
        a => !existingKeys.has(`${a.roleId}:${a.permissionId}`)
      );

      let created = 0;
      if (newAssignments.length > 0) {
        // Perform bulk insert
        await this.prisma.rolePermission.createMany({
          data: newAssignments.map(a => ({
            role_id: a.roleId,
            permission_id: a.permissionId,
          })),
        });
        created = newAssignments.length;
      }

      const skipped = assignments.length - created;

      return { created, skipped };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }
      
      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      console.error('Error performing bulk role-permission assignment:', error);
      throw new BadRequestException('Failed to perform bulk assignment');
    }
  }

  /**
   * Performs bulk removal of role-permission relationships
   *
   * @param bulkRolePermissionDto - Bulk removal data
   * @returns Promise<{ removed: number, notFound: number }> - Operation results
   * @throws BadRequestException if database operation fails
   */
  async bulkRemove(bulkRolePermissionDto: BulkRolePermissionDto): Promise<{ removed: number; notFound: number }> {
    try {
      const { assignments } = bulkRolePermissionDto;

      // Check which assignments actually exist
      const existingAssignments = await this.prisma.rolePermission.findMany({
        where: {
          OR: assignments.map(a => ({
            role_id: a.roleId,
            permission_id: a.permissionId,
          })),
        },
        select: {
          role_id: true,
          permission_id: true,
        },
      });

      const existingKeys = new Set(
        existingAssignments.map(ea => `${ea.role_id}:${ea.permission_id}`)
      );

      // Filter assignments that actually exist
      const assignmentsToRemove = assignments.filter(
        a => existingKeys.has(`${a.roleId}:${a.permissionId}`)
      );

      let removed = 0;
      if (assignmentsToRemove.length > 0) {
        // Perform bulk delete
        await this.prisma.rolePermission.deleteMany({
          where: {
            OR: assignmentsToRemove.map(a => ({
              role_id: a.roleId,
              permission_id: a.permissionId,
            })),
          },
        });
        removed = assignmentsToRemove.length;
      }

      const notFound = assignments.length - removed;

      return { removed, notFound };
    } catch (error) {
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error performing bulk role-permission removal:', error);
      throw new BadRequestException('Failed to perform bulk removal');
    }
  }

  /**
   * Copies permissions from one role to another
   *
   * @param copyPermissionsDto - Copy operation data
   * @returns Promise<{ copied: number, skipped: number }> - Operation results
   * @throws NotFoundException if source or target role doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async copyPermissions(copyPermissionsDto: CopyPermissionsDto): Promise<{ copied: number; skipped: number }> {
    try {
      const { sourceRoleId, targetRoleId, replaceExisting = false } = copyPermissionsDto;

      // Validate both roles exist
      const [sourceRole, targetRole] = await Promise.all([
        this.prisma.role.findUnique({ where: { id: sourceRoleId } }),
        this.prisma.role.findUnique({ where: { id: targetRoleId } }),
      ]);

      if (!sourceRole) {
        throw new NotFoundException(`Source role with ID '${sourceRoleId}' not found`);
      }

      if (!targetRole) {
        throw new NotFoundException(`Target role with ID '${targetRoleId}' not found`);
      }

      // Get source role permissions
      const sourcePermissions = await this.prisma.rolePermission.findMany({
        where: { role_id: sourceRoleId },
        select: { permission_id: true },
      });

      if (sourcePermissions.length === 0) {
        return { copied: 0, skipped: 0 };
      }

      const sourcePermissionIds = sourcePermissions.map(sp => sp.permission_id);

      // Perform copy operation in a transaction
      const result = await this.prisma.$transaction(async (tx) => {
        let copied = 0;
        let skipped = 0;

        if (replaceExisting) {
          // Remove all existing permissions from target role
          await tx.rolePermission.deleteMany({
            where: { role_id: targetRoleId },
          });

          // Add all source permissions to target role
          await tx.rolePermission.createMany({
            data: sourcePermissionIds.map(permissionId => ({
              role_id: targetRoleId,
              permission_id: permissionId,
            })),
          });

          copied = sourcePermissionIds.length;
        } else {
          // Get existing permissions in target role
          const existingTargetPermissions = await tx.rolePermission.findMany({
            where: { role_id: targetRoleId },
            select: { permission_id: true },
          });

          const existingPermissionIds = new Set(
            existingTargetPermissions.map(etp => etp.permission_id)
          );

          // Filter out permissions that already exist in target role
          const newPermissionIds = sourcePermissionIds.filter(
            id => !existingPermissionIds.has(id)
          );

          if (newPermissionIds.length > 0) {
            await tx.rolePermission.createMany({
              data: newPermissionIds.map(permissionId => ({
                role_id: targetRoleId,
                permission_id: permissionId,
              })),
            });
          }

          copied = newPermissionIds.length;
          skipped = sourcePermissionIds.length - copied;
        }

        return { copied, skipped };
      });

      return result;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error copying permissions between roles:', error);
      throw new BadRequestException('Failed to copy permissions');
    }
  }

  /**
   * Retrieves analytics about role-permission relationships
   *
   * @returns Promise<RolePermissionAnalyticsDto> - Analytics data
   * @throws BadRequestException if database operation fails
   */
  async getAnalytics(): Promise<RolePermissionAnalyticsDto> {
    try {
      const [
        totalRelationships,
        rolesWithPermissions,
        assignedPermissions,
        roleStats,
        permissionStats,
      ] = await Promise.all([
        // Total role-permission relationships
        this.prisma.rolePermission.count(),

        // Unique roles with at least one permission
        this.prisma.role.count({
          where: {
            role_permissions: {
              some: {},
            },
          },
        }),

        // Unique permissions assigned to at least one role
        this.prisma.permission.count({
          where: {
            role_permissions: {
              some: {},
            },
          },
        }),

        // Role statistics for average calculations
        this.prisma.role.findMany({
          select: {
            _count: {
              select: {
                role_permissions: true,
              },
            },
          },
        }),

        // Permission statistics for average calculations
        this.prisma.permission.findMany({
          select: {
            _count: {
              select: {
                role_permissions: true,
              },
            },
          },
        }),
      ]);

      // Calculate averages
      const totalRoles = roleStats.length;
      const totalPermissions = permissionStats.length;

      const avgPermissionsPerRole = totalRoles > 0
        ? roleStats.reduce((sum, role) => sum + (role as any)._count.role_permissions, 0) / totalRoles
        : 0;

      const avgRolesPerPermission = totalPermissions > 0
        ? permissionStats.reduce((sum, permission) => sum + (permission as any)._count.role_permissions, 0) / totalPermissions
        : 0;

      return {
        totalRelationships,
        rolesWithPermissions,
        assignedPermissions,
        avgPermissionsPerRole: Math.round(avgPermissionsPerRole * 100) / 100, // Round to 2 decimal places
        avgRolesPerPermission: Math.round(avgRolesPerPermission * 100) / 100, // Round to 2 decimal places
      };
    } catch (error) {
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error fetching role-permission analytics:', error);
      throw new BadRequestException('Failed to fetch analytics');
    }
  }
}
