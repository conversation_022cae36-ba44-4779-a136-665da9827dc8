import { Module } from '@nestjs/common';
import { RolePermissionsService } from './role-permissions.service';
import { RolePermissionsController } from './role-permissions.controller';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * Role Permissions Module
 * 
 * This module handles the many-to-many relationship between roles and permissions.
 * It provides specialized endpoints for:
 * - Bulk role-permission operations
 * - Advanced querying and reporting
 * - Role-permission relationship analytics
 * - Administrative management of permissions across roles
 * 
 * @module RolePermissionsModule
 */
@Module({
  imports: [PrismaModule],
  providers: [RolePermissionsService],
  controllers: [RolePermissionsController],
  exports: [RolePermissionsService], // Export service for use in other modules
})
export class RolePermissionsModule {}
