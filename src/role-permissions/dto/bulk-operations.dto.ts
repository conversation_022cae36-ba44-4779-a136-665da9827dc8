import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>yNotEmpty, ValidateNested, Matches } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for a single role-permission assignment
 *
 * @class RolePermissionAssignmentDto
 */
export class RolePermissionAssignmentDto {
  @ApiProperty({
    description: 'Role UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID('4', { message: 'Role ID must be a valid UUID' })
  roleId: string;

  @ApiProperty({
    description: 'Custom permission ID',
    example: 'users.create',
  })
  @IsString({ message: 'Permission ID must be a string' })
  @Matches(/^[a-z][a-z0-9]*(\.[a-z][a-z0-9]*)*$/, {
    message: 'Permission ID must be lowercase, start with a letter, and use dots for hierarchy (e.g., "users.create")'
  })
  permissionId: string;
}

/**
 * Data Transfer Object for bulk role-permission operations
 * 
 * This DTO allows for efficient bulk assignment or removal of multiple
 * role-permission relationships in a single operation.
 * 
 * @class BulkRolePermissionDto
 */
export class BulkRolePermissionDto {
  @ApiProperty({
    description: 'Array of role-permission assignments',
    type: [RolePermissionAssignmentDto],
    example: [
      {
        roleId: '550e8400-e29b-41d4-a716-************',
        permissionId: 'users.create'
      },
      {
        roleId: '550e8400-e29b-41d4-a716-************',
        permissionId: 'users.read'
      }
    ],
  })
  @IsArray({ message: 'Assignments must be an array' })
  @ArrayNotEmpty({ message: 'At least one assignment is required' })
  @ValidateNested({ each: true })
  @Type(() => RolePermissionAssignmentDto)
  assignments: RolePermissionAssignmentDto[];
}

/**
 * Data Transfer Object for copying permissions from one role to another
 * 
 * @class CopyPermissionsDto
 */
export class CopyPermissionsDto {
  @ApiProperty({
    description: 'Source role UUID (permissions will be copied from this role)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID('4', { message: 'Source role ID must be a valid UUID' })
  sourceRoleId: string;

  @ApiProperty({
    description: 'Target role UUID (permissions will be copied to this role)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID('4', { message: 'Target role ID must be a valid UUID' })
  targetRoleId: string;

  @ApiProperty({
    description: 'Whether to replace existing permissions in target role (default: false)',
    example: false,
    required: false,
  })
  replaceExisting?: boolean = false;
}
