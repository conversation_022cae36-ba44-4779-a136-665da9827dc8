import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for role information in role-permission responses
 * 
 * @class RoleInfoDto
 */
export class RoleInfoDto {
  @ApiProperty({
    description: 'Unique identifier for the role',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the role',
    example: 'Manager',
  })
  name: string;

  @ApiProperty({
    description: 'Optional description of the role',
    example: 'Manager role with team management capabilities',
    nullable: true,
  })
  description: string | null;
}

/**
 * Data Transfer Object for permission information in role-permission responses
 * 
 * @class PermissionInfoDto
 */
export class PermissionInfoDto {
  @ApiProperty({
    description: 'Custom permission identifier',
    example: 'users.create',
  })
  id: string;

  @ApiProperty({
    description: 'Human-readable name of the permission',
    example: 'Create Users',
  })
  name: string;

  @ApiProperty({
    description: 'Optional description of the permission',
    example: 'Allows creating new user accounts',
    nullable: true,
  })
  description: string | null;
}

/**
 * Data Transfer Object for role-permission relationship response
 * 
 * Represents the many-to-many relationship between roles and permissions.
 * This DTO is used for detailed relationship queries and analytics.
 * 
 * @class RolePermissionResponseDto
 */
export class RolePermissionResponseDto {
  @ApiProperty({
    description: 'Role information',
    type: RoleInfoDto,
  })
  role: RoleInfoDto;

  @ApiProperty({
    description: 'Permission information',
    type: PermissionInfoDto,
  })
  permission: PermissionInfoDto;

  @ApiProperty({
    description: 'Timestamp when this role-permission relationship was created',
    example: '2024-01-15T10:30:00.000Z',
    type: 'string',
    format: 'date-time',
    nullable: true,
  })
  assignedAt: Date | null;
}

/**
 * Data Transfer Object for role-permission analytics
 * 
 * Provides statistical information about role-permission relationships.
 * 
 * @class RolePermissionAnalyticsDto
 */
export class RolePermissionAnalyticsDto {
  @ApiProperty({
    description: 'Total number of role-permission relationships',
    example: 156,
  })
  totalRelationships: number;

  @ApiProperty({
    description: 'Number of unique roles with permissions',
    example: 12,
  })
  rolesWithPermissions: number;

  @ApiProperty({
    description: 'Number of unique permissions assigned to roles',
    example: 45,
  })
  assignedPermissions: number;

  @ApiProperty({
    description: 'Average number of permissions per role',
    example: 13.5,
  })
  avgPermissionsPerRole: number;

  @ApiProperty({
    description: 'Average number of roles per permission',
    example: 3.2,
  })
  avgRolesPerPermission: number;
}
