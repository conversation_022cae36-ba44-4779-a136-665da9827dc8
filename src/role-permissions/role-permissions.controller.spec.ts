import { Test, TestingModule } from '@nestjs/testing';
import { RolePermissionsController } from './role-permissions.controller';
import { RolePermissionsService } from './role-permissions.service';

describe('RolePermissionsController', () => {
  let controller: RolePermissionsController;
  let service: RolePermissionsService;

  const mockRolePermissionsService = {
    findAll: jest.fn(),
    bulkAssign: jest.fn(),
    bulkRemove: jest.fn(),
    copyPermissions: jest.fn(),
    getAnalytics: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RolePermissionsController],
      providers: [
        {
          provide: RolePermissionsService,
          useValue: mockRolePermissionsService,
        },
      ],
    }).compile();

    controller = module.get<RolePermissionsController>(RolePermissionsController);
    service = module.get<RolePermissionsService>(RolePermissionsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
