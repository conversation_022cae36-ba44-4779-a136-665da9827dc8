import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Query,
  HttpCode,
  HttpStatus,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
} from '@nestjs/swagger';
import { RolePermissionsService } from './role-permissions.service';
import { BulkRolePermissionDto, CopyPermissionsDto } from './dto/bulk-operations.dto';
import { RolePermissionResponseDto, RolePermissionAnalyticsDto } from './dto/role-permission-response.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

/**
 * Role Permissions Controller
 * 
 * This controller handles HTTP requests for managing role-permission relationships.
 * It provides specialized endpoints for bulk operations, analytics, and advanced
 * relationship management that complement the individual role and permission endpoints.
 * 
 * Key features:
 * - Bulk assignment and removal operations
 * - Permission copying between roles
 * - Relationship analytics and reporting
 * - Advanced querying and filtering
 * 
 * @controller role-permissions
 */
@ApiTags('role-permissions')
@Controller('role-permissions')
export class RolePermissionsController {
  constructor(private readonly rolePermissionsService: RolePermissionsService) {}

  /**
   * Retrieves all role-permission relationships with pagination and search
   * GET /role-permissions
   */
  @Get()
  @ApiOperation({
    summary: 'Get all role-permission relationships',
    description: 'Retrieves a paginated list of role-permission relationships with optional search functionality. Search works across role names, permission names, and descriptions.'
  })
  @ApiResponse({
    status: 200,
    description: 'Role-permission relationships retrieved successfully',
    type: PaginatedResponseDto<RolePermissionResponseDto>,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page (default: 10, max: 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term to filter by role or permission names/descriptions',
    example: 'admin',
  })
  @ApiBadRequestResponse({ description: 'Invalid query parameters or database connection failed' })
  async findAll(
    @Query(ValidationPipe) paginationDto: PaginationDto
  ): Promise<PaginatedResponseDto<RolePermissionResponseDto>> {
    return this.rolePermissionsService.findAll(paginationDto);
  }

  /**
   * Performs bulk assignment of role-permission relationships
   * POST /role-permissions/bulk-assign
   */
  @Post('bulk-assign')
  @ApiOperation({
    summary: 'Bulk assign permissions to roles',
    description: 'Assigns multiple permissions to multiple roles in a single operation. Existing assignments are skipped to prevent conflicts.'
  })
  @ApiResponse({
    status: 200,
    description: 'Bulk assignment completed successfully',
    schema: {
      type: 'object',
      properties: {
        created: {
          type: 'number',
          description: 'Number of new assignments created',
          example: 5,
        },
        skipped: {
          type: 'number',
          description: 'Number of assignments skipped (already existed)',
          example: 2,
        },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Invalid role or permission IDs' })
  @ApiBadRequestResponse({ description: 'Invalid input data or database connection failed' })
  async bulkAssign(
    @Body(ValidationPipe) bulkRolePermissionDto: BulkRolePermissionDto
  ): Promise<{ created: number; skipped: number }> {
    return this.rolePermissionsService.bulkAssign(bulkRolePermissionDto);
  }

  /**
   * Performs bulk removal of role-permission relationships
   * DELETE /role-permissions/bulk-remove
   */
  @Delete('bulk-remove')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Bulk remove permissions from roles',
    description: 'Removes multiple permission assignments from multiple roles in a single operation. Non-existent assignments are ignored.'
  })
  @ApiResponse({
    status: 200,
    description: 'Bulk removal completed successfully',
    schema: {
      type: 'object',
      properties: {
        removed: {
          type: 'number',
          description: 'Number of assignments removed',
          example: 3,
        },
        notFound: {
          type: 'number',
          description: 'Number of assignments that were not found',
          example: 1,
        },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Invalid input data or database connection failed' })
  async bulkRemove(
    @Body(ValidationPipe) bulkRolePermissionDto: BulkRolePermissionDto
  ): Promise<{ removed: number; notFound: number }> {
    return this.rolePermissionsService.bulkRemove(bulkRolePermissionDto);
  }

  /**
   * Copies permissions from one role to another
   * POST /role-permissions/copy-permissions
   */
  @Post('copy-permissions')
  @ApiOperation({
    summary: 'Copy permissions between roles',
    description: 'Copies all permissions from a source role to a target role. Can optionally replace existing permissions in the target role.'
  })
  @ApiResponse({
    status: 200,
    description: 'Permission copying completed successfully',
    schema: {
      type: 'object',
      properties: {
        copied: {
          type: 'number',
          description: 'Number of permissions copied',
          example: 8,
        },
        skipped: {
          type: 'number',
          description: 'Number of permissions skipped (already existed in target role)',
          example: 2,
        },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Source or target role not found' })
  @ApiBadRequestResponse({ description: 'Invalid input data or database connection failed' })
  async copyPermissions(
    @Body(ValidationPipe) copyPermissionsDto: CopyPermissionsDto
  ): Promise<{ copied: number; skipped: number }> {
    return this.rolePermissionsService.copyPermissions(copyPermissionsDto);
  }

  /**
   * Retrieves analytics about role-permission relationships
   * GET /role-permissions/analytics
   */
  @Get('analytics')
  @ApiOperation({
    summary: 'Get role-permission analytics',
    description: 'Retrieves statistical information about role-permission relationships including totals, averages, and distribution metrics.'
  })
  @ApiResponse({
    status: 200,
    description: 'Analytics retrieved successfully',
    type: RolePermissionAnalyticsDto,
  })
  @ApiBadRequestResponse({ description: 'Database connection failed' })
  async getAnalytics(): Promise<RolePermissionAnalyticsDto> {
    return this.rolePermissionsService.getAnalytics();
  }
}
