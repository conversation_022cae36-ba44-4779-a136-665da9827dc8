import { Controller, Get, HttpStatus } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { PurposesService } from './purposes.service';
import { PurposeResponseDto } from './dto/purpose-response.dto';

/**
 * Controller handling all purpose-related HTTP endpoints
 * Provides read-only API for purposes of activities
 */
@ApiTags('Purposes')
@Controller('purposes')
export class PurposesController {
  constructor(private readonly purposesService: PurposesService) {}

  /**
   * Retrieves all purposes
   * GET /purposes
   */
  @Get()
  @ApiOperation({
    summary: 'Get all purposes',
    description:
      'Retrieves a list of all purposes of activities. Returns only purposes with valid names, sorted alphabetically.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Purposes retrieved successfully',
    type: [PurposeResponseDto],
  })
  async findAll(): Promise<PurposeResponseDto[]> {
    return this.purposesService.findAll();
  }
}
