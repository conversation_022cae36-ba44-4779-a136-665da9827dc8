import { Module } from '@nestjs/common';
import { PurposesController } from './purposes.controller';
import { PurposesService } from './purposes.service';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * Module for handling purpose-related functionality
 * Provides read-only operations for purposes of activities
 */
@Module({
  imports: [PrismaModule],
  controllers: [PurposesController],
  providers: [PurposesService],
  exports: [PurposesService],
})
export class PurposesModule {}
