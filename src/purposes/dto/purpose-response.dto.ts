import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for purpose response
 * Used to return purpose data in API responses
 */
export class PurposeResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the purpose',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the purpose',
    example: 'Follow-up Call',
  })
  name: string;
}
