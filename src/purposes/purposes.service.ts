import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { PurposeResponseDto } from './dto/purpose-response.dto';

/**
 * Service responsible for handling all purpose-related business logic
 * Provides read operations for purposes of activities
 */
@Injectable()
export class PurposesService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Retrieves all purposes
   * @returns Promise<PurposeResponseDto[]> - List of all purposes with id and name
   */
  async findAll(): Promise<PurposeResponseDto[]> {
    const purposes = await this.prisma.purposeOfActivity.findMany({
      select: {
        id: true,
        name: true,
      },
      orderBy: {
        name: 'asc', // Sort alphabetically by name
      },
    });

    // Transform to response DTOs, filtering out purposes with null names
    return purposes
      .filter((purpose) => purpose.name !== null)
      .map((purpose) => ({
        id: purpose.id,
        name: purpose.name!,
      }));
  }
}
