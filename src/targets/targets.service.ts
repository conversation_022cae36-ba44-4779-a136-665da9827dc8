import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateTargetDto } from './dto/create-target.dto';
import { UpdateTargetDto } from './dto/update-target.dto';
import { TargetResponseDto } from './dto/target-response.dto';
import { TargetBreakdownResponseDto } from './dto/target-breakdown-response.dto';

/**
 * Service handling target-related business logic
 * Provides operations for creating and managing targets
 */
@Injectable()
export class TargetsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates targets based on the provided data
   * @param createTargetDto - Data for creating targets
   * @returns Promise<void> - Confirmation of target creation
   */
  async createTargets(createTargetDto: CreateTargetDto): Promise<void> {
    const {
      metricType,
      targetValue,
      frequency,
      startDate,
      endDate,
      scope,
      assignTo,
    } = createTargetDto;

    // Validate date range
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (start >= end) {
      throw new BadRequestException('Start date must be before end date');
    }

    // Validate assignTo IDs exist based on scope
    if (scope === 'role') {
      const roles = await this.prisma.role.findMany({
        where: { id: { in: assignTo } },
        select: { id: true },
      });

      if (roles.length !== assignTo.length) {
        const foundIds = roles.map((role) => role.id);
        const missingIds = assignTo.filter((id) => !foundIds.includes(id));
        throw new NotFoundException(
          `Roles not found: ${missingIds.join(', ')}`,
        );
      }
    } else {
      const users = await this.prisma.user.findMany({
        where: { id: { in: assignTo } },
        select: { id: true },
      });

      if (users.length !== assignTo.length) {
        const foundIds = users.map((user) => user.id);
        const missingIds = assignTo.filter((id) => !foundIds.includes(id));
        throw new NotFoundException(
          `Users not found: ${missingIds.join(', ')}`,
        );
      }
    }

    // Create targets for each assignTo ID
    const targetsData = assignTo.map((id) => ({
      metric_type: metricType as 'Call' | 'Visit',
      target_value: targetValue,
      frequency: frequency as 'daily' | 'weekly' | 'custom',
      start_date: start,
      end_date: end,
      ...(scope === 'role' ? { role_id: id } : { user_id: id }),
    }));

    await this.prisma.target.createMany({
      data: targetsData,
    });
  }

  /**
   * Gets all targets formatted for UI display
   * @returns Promise<TargetResponseDto[]> - Array of formatted targets
   */
  async getAllTargets(): Promise<TargetResponseDto[]> {
    const targets = await this.prisma.target.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            role: {
              select: {
                name: true,
              },
            },
          },
        },
        role: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    return Promise.all(
      targets.map(async (target) => {
        const progress = await this.calculateProgress(target);
        const isIndividual = target.user_id !== null;

        // Get activity count for individual targets
        let activityCount: number | undefined;
        if (isIndividual && target.user_id) {
          const interactionType = target.metric_type.toLowerCase() as
            | 'call'
            | 'visit';
          activityCount = await this.prisma.activity.count({
            where: {
              performed_by_user_id: target.user_id,
              interaction_type: interactionType,
              created_at: {
                gte: target.start_date,
                lte: target.end_date,
              },
            },
          });
        }

        // Format assigned_to based on scope
        const assigned_to = target.user
          ? {
              id: target.user.id,
              name: target.user.name,
              role: target.user.role?.name,
            }
          : target.role?.name || 'Unknown';

        return {
          id: target.id,
          metric: target.metric_type,
          assigned_to,
          frequency: target.frequency,
          value: target.target_value,
          start_date: target.start_date.toISOString().split('T')[0],
          end_date: target.end_date.toISOString().split('T')[0],
          progress,
          scope: target.role_id ? 'role' : 'individual',
          ...(isIndividual && { activity_count: activityCount }),
        };
      }),
    );
  }

  /**
   * Updates a target by ID
   * @param id - Target ID to update
   * @param updateTargetDto - Data to update
   * @returns Promise<void> - Confirmation of target update
   */
  async updateTarget(
    id: string,
    updateTargetDto: UpdateTargetDto,
  ): Promise<void> {
    const { metricType, targetValue, frequency, startDate, endDate } =
      updateTargetDto;

    // Check if target exists
    const existingTarget = await this.prisma.target.findUnique({
      where: { id },
    });

    if (!existingTarget) {
      throw new NotFoundException(`Target with ID '${id}' not found`);
    }

    // Validate date range if both dates are provided
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (start >= end) {
        throw new BadRequestException('Start date must be before end date');
      }
    }

    // Build update data object
    const updateData: any = {};

    if (metricType !== undefined) {
      updateData.metric_type = metricType as 'Call' | 'Visit';
    }
    if (targetValue !== undefined) {
      updateData.target_value = targetValue;
    }
    if (frequency !== undefined) {
      updateData.frequency = frequency as 'daily' | 'weekly' | 'custom';
    }
    if (startDate !== undefined) {
      updateData.start_date = new Date(startDate);
    }
    if (endDate !== undefined) {
      updateData.end_date = new Date(endDate);
    }

    // Update the target
    await this.prisma.target.update({
      where: { id },
      data: updateData,
    });
  }

  /**
   * Deletes a target by ID
   * @param id - Target ID to delete
   * @returns Promise<void> - Confirmation of target deletion
   */
  async deleteTarget(id: string): Promise<void> {
    // Check if target exists
    const existingTarget = await this.prisma.target.findUnique({
      where: { id },
    });

    if (!existingTarget) {
      throw new NotFoundException(`Target with ID '${id}' not found`);
    }

    // Delete the target
    await this.prisma.target.delete({
      where: { id },
    });
  }

  /**
   * Gets breakdown of users for a role target
   * @param id - Target ID (must be a role target)
   * @returns Promise<TargetBreakdownResponseDto[]> - Array of user breakdowns
   */
  async getTargetBreakdown(id: string): Promise<TargetBreakdownResponseDto[]> {
    // Get the target and ensure it's a role target
    const target = await this.prisma.target.findUnique({
      where: { id },
      include: {
        role: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!target) {
      throw new NotFoundException(`Target with ID '${id}' not found`);
    }

    if (!target.role_id) {
      throw new BadRequestException(
        'Target breakdown is only available for role targets',
      );
    }

    // Get all users with this role
    const users = await this.prisma.user.findMany({
      where: {
        role_id: target.role_id,
      },
      select: {
        id: true,
        name: true,
      },
    });

    // Calculate calls and visits for each user within the target period
    const breakdown = await Promise.all(
      users.map(async (user) => {
        const [callsCount, visitsCount] = await Promise.all([
          this.prisma.activity.count({
            where: {
              performed_by_user_id: user.id,
              interaction_type: 'call',
              created_at: {
                gte: target.start_date,
                lte: target.end_date,
              },
            },
          }),
          this.prisma.activity.count({
            where: {
              performed_by_user_id: user.id,
              interaction_type: 'visit',
              created_at: {
                gte: target.start_date,
                lte: target.end_date,
              },
            },
          }),
        ]);

        return {
          user_id: user.id,
          name: user.name,
          calls_made: callsCount,
          visits_made: visitsCount,
          target: target.target_value,
        };
      }),
    );

    return breakdown;
  }

  /**
   * Calculates progress percentage for a target
   * @param target - Target object with relations
   * @returns Promise<number> - Progress percentage (0-100)
   */
  private async calculateProgress(target: any): Promise<number> {
    // For now, return a placeholder progress calculation
    // In a real implementation, you would calculate based on actual activities

    const now = new Date();
    const start = target.start_date;
    const end = target.end_date;

    // If target period hasn't started yet
    if (now < start) {
      return 0;
    }

    // If target period has ended
    if (now > end) {
      return 100; // Or calculate actual completion
    }

    // Calculate time-based progress as placeholder
    const totalDuration = end.getTime() - start.getTime();
    const elapsed = now.getTime() - start.getTime();
    const timeProgress = Math.min((elapsed / totalDuration) * 100, 100);

    // Return a random progress between 0 and time progress for demo
    return Math.floor(Math.random() * timeProgress);
  }
}
