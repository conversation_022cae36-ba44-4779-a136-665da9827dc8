import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for target breakdown response
 * Returns user breakdown for role targets
 */
export class TargetBreakdownResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  user_id: string;

  @ApiProperty({
    description: 'User name',
    example: '<PERSON>',
  })
  name: string;

  @ApiProperty({
    description: 'Number of calls made within the target period',
    example: 15,
  })
  calls_made: number;

  @ApiProperty({
    description: 'Number of visits made within the target period',
    example: 8,
  })
  visits_made: number;

  @ApiProperty({
    description: 'Target value for this user',
    example: 25,
  })
  target: number;
}
