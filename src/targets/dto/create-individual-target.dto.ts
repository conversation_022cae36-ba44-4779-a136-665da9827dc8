import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for creating individual target from existing target
 */
export class CreateIndividualTargetDto {
  @ApiProperty({
    description: 'New target value for the individual target',
    example: '10',
  })
  @IsString({ message: 'New target must be a string' })
  newTarget: string;

  @ApiProperty({
    description: 'User ID to assign the individual target to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsString({ message: 'User ID must be a string' })
  @IsUUID(4, { message: 'User ID must be a valid UUID' })
  userId: string;
}
