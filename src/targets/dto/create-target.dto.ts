import {
  IsString,
  <PERSON><PERSON>umber,
  IsDateString,
  IsIn,
  IsArray,
  IsUUID,
  ArrayNotEmpty,
  Min,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

/**
 * Data Transfer Object for creating targets
 */
export class CreateTargetDto {
  @ApiProperty({
    description: 'Type of metric to track',
    example: 'Call',
    enum: ['Call', 'Visit'],
  })
  @IsString({ message: 'Metric type must be a string' })
  @IsIn(['Call', 'Visit'], {
    message: 'Metric type must be either "Call" or "Visit"',
  })
  metricType: string;

  @ApiProperty({
    description: 'Target value to achieve',
    example: 25,
    minimum: 1,
  })
  @IsNumber({}, { message: 'Target value must be a number' })
  @Min(1, { message: 'Target value must be at least 1' })
  targetValue: number;

  @ApiProperty({
    description: 'Frequency of the target',
    example: 'weekly',
    enum: ['daily', 'weekly', 'custom'],
  })
  @IsString({ message: 'Frequency must be a string' })
  @IsIn(['daily', 'weekly', 'custom'], {
    message: 'Frequency must be either "daily", "weekly", or "custom"',
  })
  frequency: string;

  @ApiProperty({
    description: 'Start date of the target period',
    example: '2025-08-01',
  })
  @IsDateString({}, { message: 'Start date must be a valid date string' })
  startDate: string;

  @ApiProperty({
    description: 'End date of the target period',
    example: '2025-08-31',
  })
  @IsDateString({}, { message: 'End date must be a valid date string' })
  endDate: string;

  @ApiProperty({
    description: 'Scope of assignment - either "Role" or "Individual"',
    example: 'Role',
    enum: ['Role', 'Individual'],
  })
  @IsString({ message: 'Scope must be a string' })
  @Transform(({ value }) => value.toLowerCase())
  @IsIn(['role', 'individual'], {
    message: 'Scope must be either "Role" or "Individual" (case-insensitive)',
  })
  scope: string;

  @ApiProperty({
    description:
      'Array of UUIDs to assign targets to (roles or users based on scope)',
    example: [
      '550e8400-e29b-41d4-a716-************',
      '660e8400-e29b-41d4-a716-************',
    ],
    type: [String],
  })
  @IsArray({ message: 'AssignTo must be an array' })
  @ArrayNotEmpty({ message: 'AssignTo array cannot be empty' })
  @IsUUID(4, { each: true, message: 'Each assignTo item must be a valid UUID' })
  assignTo: string[];
}
