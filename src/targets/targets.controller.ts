import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  HttpCode,
  HttpStatus,
  ValidationPipe,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiNoContentResponse,
} from '@nestjs/swagger';
import { TargetsService } from './targets.service';
import { CreateTargetDto } from './dto/create-target.dto';
import { UpdateTargetDto } from './dto/update-target.dto';
import { TargetResponseDto } from './dto/target-response.dto';
import { TargetBreakdownResponseDto } from './dto/target-breakdown-response.dto';

/**
 * Controller handling target-related HTTP endpoints
 * Provides endpoints for creating and retrieving targets
 */
@ApiTags('Targets')
@Controller('targets')
export class TargetsController {
  constructor(private readonly targetsService: TargetsService) {}

  /**
   * Creates targets based on shared input
   * POST /targets
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create targets',
    description:
      'Creates targets based on shared input. For each ID in assignTo, creates a Target with the same data and assigns either roleId or userId appropriately based on scope.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Targets created successfully',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or start date is not before end date',
  })
  @ApiNotFoundResponse({
    description: 'One or more roles/users not found',
  })
  async createTargets(
    @Body(ValidationPipe) createTargetDto: CreateTargetDto,
  ): Promise<void> {
    return this.targetsService.createTargets(createTargetDto);
  }

  /**
   * Gets all targets formatted for UI display
   * GET /targets
   */
  @Get()
  @ApiOperation({
    summary: 'Get all targets',
    description:
      'Returns all targets formatted for UI display. Each target includes progress calculation and scope information.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Targets retrieved successfully',
    type: [TargetResponseDto],
  })
  async getAllTargets(): Promise<TargetResponseDto[]> {
    return this.targetsService.getAllTargets();
  }

  /**
   * Updates a target by ID
   * PATCH /targets/:id
   */
  @Patch(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Update target',
    description:
      'Updates a target by ID with the provided fields. Only the fields provided in the request body will be updated.',
  })
  @ApiParam({ name: 'id', description: 'Target UUID' })
  @ApiNoContentResponse({
    description: 'Target updated successfully',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or start date is not before end date',
  })
  @ApiNotFoundResponse({
    description: 'Target not found',
  })
  async updateTarget(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateTargetDto: UpdateTargetDto,
  ): Promise<void> {
    return this.targetsService.updateTarget(id, updateTargetDto);
  }

  /**
   * Deletes a target by ID
   * DELETE /targets/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete target',
    description: 'Deletes a target by ID.',
  })
  @ApiParam({ name: 'id', description: 'Target UUID' })
  @ApiNoContentResponse({
    description: 'Target deleted successfully',
  })
  @ApiNotFoundResponse({
    description: 'Target not found',
  })
  async deleteTarget(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.targetsService.deleteTarget(id);
  }

  /**
   * Gets breakdown of users in a role target
   * GET /targets/:id/breakdown
   */
  @Get(':id/breakdown')
  @ApiOperation({
    summary: 'Get target breakdown',
    description:
      'Returns all users of a role with their activity counts within the target period. Only available for role targets.',
  })
  @ApiParam({ name: 'id', description: 'Role target UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Target breakdown retrieved successfully',
    type: [TargetBreakdownResponseDto],
  })
  @ApiNotFoundResponse({
    description: 'Target not found',
  })
  @ApiBadRequestResponse({
    description: 'Target breakdown is only available for role targets',
  })
  async getTargetBreakdown(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<TargetBreakdownResponseDto[]> {
    return this.targetsService.getTargetBreakdown(id);
  }
}
