import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
} from '@nestjs/swagger';
import { CustomerCategoriesService } from './customer-categories.service';
import { CreateCustomerCategoryDto } from './dto/create-customer-category.dto';
import { UpdateCustomerCategoryDto } from './dto/update-customer-category.dto';
import { CustomerCategoryResponseDto } from './dto/customer-category-response.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

/**
 * Controller handling all customer category-related HTTP endpoints
 * Provides RESTful API for customer category management
 */
@ApiTags('Customer Categories')
@Controller('customer-categories')
export class CustomerCategoriesController {
  constructor(private readonly customerCategoriesService: CustomerCategoriesService) {}

  /**
   * Creates a new customer category
   * POST /customer-categories
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new customer category',
    description: 'Creates a new customer category with the provided name. Category names must be unique (case-insensitive).'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Customer category created successfully',
    type: CustomerCategoryResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiConflictResponse({ description: 'Customer category name already exists' })
  async create(
    @Body(ValidationPipe) createCustomerCategoryDto: CreateCustomerCategoryDto
  ): Promise<CustomerCategoryResponseDto> {
    return this.customerCategoriesService.create(createCustomerCategoryDto);
  }

  /**
   * Retrieves all customer categories with pagination and search
   * GET /customer-categories
   */
  @Get()
  @ApiOperation({
    summary: 'Get all customer categories',
    description: 'Retrieves a paginated list of customer categories with optional search functionality. Includes lead count for each category.'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10, max: 100)' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term for category names' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer categories retrieved successfully',
    type: PaginatedResponseDto<CustomerCategoryResponseDto>,
  })
  async findAll(
    @Query(ValidationPipe) paginationDto: PaginationDto
  ): Promise<PaginatedResponseDto<CustomerCategoryResponseDto>> {
    return this.customerCategoriesService.findAll(paginationDto);
  }

  /**
   * Retrieves a single customer category by ID
   * GET /customer-categories/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get customer category by ID',
    description: 'Retrieves a single customer category by its UUID. Includes lead count and basic category information.'
  })
  @ApiParam({ name: 'id', description: 'Customer category UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer category retrieved successfully',
    type: CustomerCategoryResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Customer category not found' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<CustomerCategoryResponseDto> {
    return this.customerCategoriesService.findOne(id);
  }

  /**
   * Updates an existing customer category
   * PATCH /customer-categories/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update customer category',
    description: 'Updates an existing customer category. Only provided fields will be updated. Category names must remain unique.'
  })
  @ApiParam({ name: 'id', description: 'Customer category UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer category updated successfully',
    type: CustomerCategoryResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Customer category not found' })
  @ApiConflictResponse({ description: 'Customer category name already exists' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateCustomerCategoryDto: UpdateCustomerCategoryDto
  ): Promise<CustomerCategoryResponseDto> {
    return this.customerCategoriesService.update(id, updateCustomerCategoryDto);
  }

  /**
   * Deletes a customer category
   * DELETE /customer-categories/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete customer category',
    description: 'Deletes a customer category by ID. Cannot delete categories that have associated leads.'
  })
  @ApiParam({ name: 'id', description: 'Customer category UUID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Customer category deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Customer category not found' })
  @ApiConflictResponse({ description: 'Customer category has associated leads and cannot be deleted' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.customerCategoriesService.remove(id);
  }

  /**
   * Retrieves all leads for a specific customer category
   * GET /customer-categories/:id/leads
   */
  @Get(':id/leads')
  @ApiOperation({
    summary: 'Get leads by customer category',
    description: 'Retrieves all leads belonging to a specific customer category with pagination and search.'
  })
  @ApiParam({ name: 'id', description: 'Customer category UUID' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10, max: 100)' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term for customer name, client ID, or phone number' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Leads retrieved successfully',
  })
  @ApiNotFoundResponse({ description: 'Customer category not found' })
  async getCategoryLeads(
    @Param('id', ParseUUIDPipe) id: string,
    @Query(ValidationPipe) paginationDto: PaginationDto
  ) {
    return this.customerCategoriesService.getCategoryLeads(id, paginationDto);
  }
}