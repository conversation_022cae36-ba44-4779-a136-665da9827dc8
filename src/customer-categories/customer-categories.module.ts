import { Module } from '@nestjs/common';
import { CustomerCategoriesService } from './customer-categories.service';
import { CustomerCategoriesController } from './customer-categories.controller';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * Module for Customer Categories functionality
 * Provides CRUD operations for customer category management
 */
@Module({
  imports: [PrismaModule],
  providers: [CustomerCategoriesService],
  controllers: [CustomerCategoriesController],
  exports: [CustomerCategoriesService], // Export service for use in other modules
})
export class CustomerCategoriesModule {}
