import { PartialType } from '@nestjs/mapped-types';
import { CreateCustomerCategoryDto } from './create-customer-category.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for updating an existing customer category
 * Extends CreateCustomerCategoryDto but makes all fields optional
 */
export class UpdateCustomerCategoryDto extends PartialType(CreateCustomerCategoryDto) {
  @ApiPropertyOptional({
    description: 'The name of the customer category',
    example: 'Updated Corporate',
    maxLength: 255,
  })
  name?: string;
}
