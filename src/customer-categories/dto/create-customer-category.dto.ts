import { IsNotEmpty, <PERSON>S<PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for creating a new customer category
 * Validates input data and provides API documentation
 */
export class CreateCustomerCategoryDto {
  @ApiProperty({
    description: 'The name of the customer category',
    example: 'Corporate',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Customer category name is required' })
  @IsString({ message: 'Customer category name must be a string' })
  @MaxLength(255, { message: 'Customer category name cannot exceed 255 characters' })
  name: string;
}
