import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for customer category response
 * Defines the structure of customer category data returned by the API
 */
export class CustomerCategoryResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the customer category',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the customer category',
    example: 'Corporate',
    nullable: true,
  })
  name: string | null;

  @ApiProperty({
    description: 'Number of leads associated with this customer category',
    example: 25,
  })
  leadCount?: number;
}
