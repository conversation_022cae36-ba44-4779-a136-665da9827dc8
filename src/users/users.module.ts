import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * Users Module
 * 
 * This module encapsulates all user-related functionality including:
 * - User CRUD operations
 * - User authentication and authorization
 * - User role and branch management
 * - User activity tracking
 * 
 * @module UsersModule
 */
@Module({
  imports: [PrismaModule],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService], // Export service for use in other modules
})
export class UsersModule {}
