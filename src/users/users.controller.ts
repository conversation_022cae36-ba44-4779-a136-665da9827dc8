import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiConflictResponse,
  ApiUnauthorizedResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserResponseDto } from './dto/user-response.dto';
import { AssignRoleDto, TransferBranchDto, UpdateLastLoginDto } from './dto/user-role-assignment.dto';
import { ChangePasswordDto, ResetPasswordDto } from './dto/change-password.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

/**
 * Controller responsible for user-related HTTP endpoints
 * 
 * This controller provides RESTful API endpoints for managing users,
 * including CRUD operations with comprehensive validation and error handling.
 * All endpoints follow REST conventions and include detailed API documentation.
 * 
 * @controller users
 */
@ApiTags('users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  /**
   * Creates a new user
   * POST /users
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new user',
    description: 'Creates a new user with role and branch assignment. Validates that the email is unique, and that the specified role and branch exist in the system.'
  })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    type: UserResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data or database connection failed' })
  @ApiConflictResponse({ description: 'Email already exists' })
  @ApiNotFoundResponse({ description: 'Role or branch not found' })
  async create(
    @Body(ValidationPipe) createUserDto: CreateUserDto
  ): Promise<UserResponseDto> {
    return this.usersService.create(createUserDto);
  }

  /**
   * Retrieves a paginated list of users
   * GET /users
   */
  @Get()
  @ApiOperation({
    summary: 'Get all users with pagination',
    description: 'Retrieves a paginated list of users with optional search functionality. Search can be performed across user name, email, RM code, role name, and branch name.'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page (default: 10, max: 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term to filter users by name, email, RM code, role, or branch',
    example: 'john',
  })
  @ApiResponse({
    status: 200,
    description: 'Users retrieved successfully',
    type: 'object',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/PaginatedResponseDto' },
        {
          properties: {
            data: {
              type: 'array',
              items: { $ref: '#/components/schemas/UserResponseDto' },
            },
          },
        },
      ],
    },
  })
  @ApiBadRequestResponse({ description: 'Invalid query parameters or database connection failed' })
  async findAll(
    @Query(ValidationPipe) paginationDto: PaginationDto
  ): Promise<PaginatedResponseDto<UserResponseDto>> {
    return this.usersService.findAll(paginationDto);
  }

  /**
   * Retrieves a single user by ID
   * GET /users/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get user by ID',
    description: 'Retrieves a single user by their UUID, including related role and branch information, as well as activity counts.'
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'User retrieved successfully',
    type: UserResponseDto,
  })
  @ApiNotFoundResponse({ description: 'User not found' })
  @ApiBadRequestResponse({ description: 'Invalid UUID format or database connection failed' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<UserResponseDto> {
    return this.usersService.findOne(id);
  }

  /**
   * Updates an existing user
   * PATCH /users/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update user',
    description: 'Updates an existing user. Validates email uniqueness if email is being changed, and validates that role and branch exist if they are being updated.'
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully',
    type: UserResponseDto,
  })
  @ApiNotFoundResponse({ description: 'User, role, or branch not found' })
  @ApiBadRequestResponse({ description: 'Invalid input data or database connection failed' })
  @ApiConflictResponse({ description: 'Email already exists for another user' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateUserDto: UpdateUserDto
  ): Promise<UserResponseDto> {
    return this.usersService.update(id, updateUserDto);
  }

  /**
   * Deletes a user
   * DELETE /users/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete user',
    description: 'Deletes a user by ID. Prevents deletion if the user has associated activities, leads, or other data that would be orphaned.'
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 204,
    description: 'User deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'User not found' })
  @ApiBadRequestResponse({ description: 'Invalid UUID format or database connection failed' })
  @ApiConflictResponse({ description: 'User has associated data and cannot be deleted' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.usersService.remove(id);
  }

  /**
   * Assigns a role to a user
   * POST /users/:id/assign-role
   */
  @Post(':id/assign-role')
  @ApiOperation({
    summary: 'Assign role to user',
    description: 'Assigns a specific role to a user. This is a specialized endpoint for role management that validates the role exists before assignment.'
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Role assigned successfully',
    type: UserResponseDto,
  })
  @ApiNotFoundResponse({ description: 'User or role not found' })
  @ApiBadRequestResponse({ description: 'Invalid input data or database connection failed' })
  async assignRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) assignRoleDto: AssignRoleDto
  ): Promise<UserResponseDto> {
    return this.usersService.assignRole(id, assignRoleDto);
  }

  /**
   * Transfers a user to a different branch
   * POST /users/:id/transfer-branch
   */
  @Post(':id/transfer-branch')
  @ApiOperation({
    summary: 'Transfer user to different branch',
    description: 'Transfers a user to a different branch. This is useful for organizational changes and user relocations.'
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'User transferred successfully',
    type: UserResponseDto,
  })
  @ApiNotFoundResponse({ description: 'User or branch not found' })
  @ApiBadRequestResponse({ description: 'Invalid input data or database connection failed' })
  async transferBranch(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) transferBranchDto: TransferBranchDto
  ): Promise<UserResponseDto> {
    return this.usersService.transferBranch(id, transferBranchDto);
  }

  /**
   * Updates the last login timestamp for a user
   * PATCH /users/:id/last-login
   */
  @Patch(':id/last-login')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Update user last login',
    description: 'Updates the last login timestamp for a user. This endpoint is typically called by authentication middleware to track user activity.'
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Last login updated successfully',
  })
  @ApiNotFoundResponse({ description: 'User not found' })
  @ApiBadRequestResponse({ description: 'Invalid input data or database connection failed' })
  async updateLastLogin(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateLastLoginDto: UpdateLastLoginDto
  ): Promise<void> {
    return this.usersService.updateLastLogin(id, updateLastLoginDto);
  }

  /**
   * Changes a user's password
   * PATCH /users/:id/change-password
   */
  @Patch(':id/change-password')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Change user password',
    description: 'Changes a user\'s password after verifying the current password. This endpoint requires the current password for security verification.'
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Password changed successfully',
  })
  @ApiNotFoundResponse({ description: 'User not found' })
  @ApiBadRequestResponse({ description: 'Invalid input data or database connection failed' })
  @ApiUnauthorizedResponse({ description: 'Current password is incorrect' })
  async changePassword(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) changePasswordDto: ChangePasswordDto
  ): Promise<void> {
    return this.usersService.changePassword(id, changePasswordDto);
  }

  /**
   * Resets a user's password (admin operation)
   * PATCH /users/:id/reset-password
   */
  @Patch(':id/reset-password')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Reset user password (admin operation)',
    description: 'Resets a user\'s password without requiring the current password. This is an administrative operation that should be restricted to authorized personnel.'
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Password reset successfully',
  })
  @ApiNotFoundResponse({ description: 'User not found' })
  @ApiBadRequestResponse({ description: 'Invalid input data or database connection failed' })
  async resetPassword(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) resetPasswordDto: ResetPasswordDto
  ): Promise<void> {
    return this.usersService.resetPassword(id, resetPasswordDto);
  }
}
