import { PartialType } from '@nestjs/swagger';
import { CreateUserDto } from './create-user.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength, Matches } from 'class-validator';

/**
 * Data Transfer Object for updating an existing user
 * 
 * Extends CreateUserDto but makes all fields optional.
 * This allows for partial updates where only specific fields need to be modified.
 * 
 * @class UpdateUserDto
 */
export class UpdateUserDto extends PartialType(CreateUserDto) {
  @ApiPropertyOptional({
    description: 'Full name of the user',
    example: '<PERSON>',
    maxLength: 255,
  })
  name?: string;

  @ApiPropertyOptional({
    description: 'Email address of the user (must be unique)',
    example: '<EMAIL>',
    maxLength: 255,
  })
  email?: string;

  @ApiPropertyOptional({
    description: 'Phone number of the user. Supports international format (+**********) or local formats (07XXXXXXXX, 01XXXXXXXX)',
    example: '+********** or ********** or **********',
    maxLength: 20,
  })
  @IsOptional()
  @IsString({ message: 'Phone number must be a string' })
  @MaxLength(20, { message: 'Phone number cannot exceed 20 characters' })
  @Matches(/^(\+[\d]{1,4}[\d]{4,15}|0[17][\d]{8})$/, {
    message: 'Phone number must be in international format (+**********) or local format (07XXXXXXXX or 01XXXXXXXX)'
  })
  phone_number?: string;

  @ApiPropertyOptional({
    description: 'New password for the user account (will be hashed before storage)',
    example: 'NewSecurePassword123!',
    minLength: 8,
    maxLength: 100,
  })
  password?: string;

  @ApiPropertyOptional({
    description: 'Relationship Manager code for the user',
    example: 'RM002',
    maxLength: 50,
  })
  @IsOptional()
  @IsString({ message: 'RM code must be a string' })
  @MaxLength(50, { message: 'RM code cannot exceed 50 characters' })
  rm_code?: string;

  @ApiPropertyOptional({
    description: 'UUID of the role to assign to this user',
    example: '550e8400-e29b-41d4-a716-************',
  })
  role_id?: string;

  @ApiPropertyOptional({
    description: 'UUID of the branch this user belongs to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  branch_id?: string;

  @ApiPropertyOptional({
    description: 'Last login timestamp',
    example: '2024-01-15T10:30:00.000Z',
    type: 'string',
    format: 'date-time',
  })
  last_login?: Date;
}
