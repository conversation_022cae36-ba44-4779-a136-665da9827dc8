import { IsNotEmpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for changing user password
 * 
 * This DTO is used for secure password change operations where the user
 * provides their current password and a new password.
 * 
 * @class ChangePasswordDto
 */
export class ChangePasswordDto {
  @ApiProperty({
    description: 'Current password for verification',
    example: 'CurrentPassword123!',
    minLength: 8,
    maxLength: 100,
  })
  @IsNotEmpty({ message: 'Current password is required' })
  @IsString({ message: 'Current password must be a string' })
  @MinLength(8, { message: 'Current password must be at least 8 characters long' })
  @MaxLength(100, { message: 'Current password cannot exceed 100 characters' })
  currentPassword: string;

  @ApiProperty({
    description: 'New password for the user account (will be hashed before storage)',
    example: 'NewSecurePassword123!',
    minLength: 8,
    maxLength: 100,
  })
  @IsNotEmpty({ message: 'New password is required' })
  @IsString({ message: 'New password must be a string' })
  @MinLength(8, { message: 'New password must be at least 8 characters long' })
  @MaxLength(100, { message: 'New password cannot exceed 100 characters' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
    message: 'New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
  })
  newPassword: string;
}

/**
 * Data Transfer Object for admin password reset
 * 
 * This DTO is used by administrators to reset a user's password
 * without requiring the current password.
 * 
 * @class ResetPasswordDto
 */
export class ResetPasswordDto {
  @ApiProperty({
    description: 'New password for the user account (will be hashed before storage)',
    example: 'AdminResetPassword123!',
    minLength: 8,
    maxLength: 100,
  })
  @IsNotEmpty({ message: 'New password is required' })
  @IsString({ message: 'New password must be a string' })
  @MinLength(8, { message: 'New password must be at least 8 characters long' })
  @MaxLength(100, { message: 'New password cannot exceed 100 characters' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
    message: 'New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
  })
  newPassword: string;
}
