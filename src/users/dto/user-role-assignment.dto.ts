import { IsUUID, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for assigning a role to a user
 * 
 * This DTO is used for role assignment operations where you need to
 * change a user's role without updating other user information.
 * 
 * @class AssignRoleDto
 */
export class AssignRoleDto {
  @ApiProperty({
    description: 'UUID of the role to assign to the user',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Role ID is required' })
  @IsUUID('4', { message: 'Role ID must be a valid UUID' })
  role_id: string;
}

/**
 * Data Transfer Object for transferring a user to a different branch
 * 
 * This DTO is used for branch transfer operations where you need to
 * move a user to a different branch without updating other user information.
 * 
 * @class TransferBranchDto
 */
export class TransferBranchDto {
  @ApiProperty({
    description: 'UUID of the branch to transfer the user to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Branch ID is required' })
  @IsUUID('4', { message: 'Branch ID must be a valid UUID' })
  branch_id: string;
}

/**
 * Data Transfer Object for updating user login timestamp
 * 
 * This DTO is used by the system to record when a user last logged in.
 * Typically called by authentication middleware.
 * 
 * @class UpdateLastLoginDto
 */
export class UpdateLastLoginDto {
  @ApiProperty({
    description: 'Timestamp of the last login',
    example: '2024-01-15T10:30:00.000Z',
    type: 'string',
    format: 'date-time',
  })
  last_login: Date;
}
