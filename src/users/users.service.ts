import { Injectable, NotFoundException, ConflictException, BadRequestException, UnauthorizedException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserResponseDto } from './dto/user-response.dto';
import { AssignRoleDto, TransferBranchDto, UpdateLastLoginDto } from './dto/user-role-assignment.dto';
import { ChangePasswordDto, ResetPasswordDto } from './dto/change-password.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { Prisma } from '@prisma/client';
import * as bcrypt from 'bcrypt';

/**
 * Service responsible for user-related operations
 * 
 * This service handles all CRUD operations for users, including:
 * - Creating new users with role and branch validation
 * - Retrieving users with pagination and search
 * - Updating user information
 * - Deleting users with dependency checks
 * - Managing user relationships with roles and branches
 * 
 * @class UsersService
 */
@Injectable()
export class UsersService {
  private readonly saltRounds = 12; // Number of salt rounds for bcrypt

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new user with role and branch validation
   * 
   * @param createUserDto - Data for creating the user
   * @returns Promise<UserResponseDto> - The created user with related data
   * @throws ConflictException if email already exists
   * @throws NotFoundException if role or branch doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async create(createUserDto: CreateUserDto): Promise<UserResponseDto> {
    try {
      // Check if email already exists
      const existingUser = await this.prisma.user.findFirst({
        where: {
          email: {
            equals: createUserDto.email,
            mode: 'insensitive',
          },
        },
      });

      if (existingUser) {
        throw new ConflictException(`User with email '${createUserDto.email}' already exists`);
      }

      // Validate that role exists
      const role = await this.prisma.role.findUnique({
        where: { id: createUserDto.role_id },
      });

      if (!role) {
        throw new NotFoundException(`Role with ID '${createUserDto.role_id}' not found`);
      }

      // Validate that branch exists
      const branch = await this.prisma.branch.findUnique({
        where: { id: createUserDto.branch_id },
        include: { region: true },
      });

      if (!branch) {
        throw new NotFoundException(`Branch with ID '${createUserDto.branch_id}' not found`);
      }

      // Hash the password before saving
      const hashedPassword = await bcrypt.hash(createUserDto.password, this.saltRounds);

      // Create the user with hashed password
      const user = await this.prisma.user.create({
        data: {
          ...createUserDto,
          password: hashedPassword,
        },
        include: {
          role: true,
          branch: {
            include: {
              region: true,
            },
          },
          _count: {
            select: {
              general_activities: true,
              leads: true,
              loan_activities: true,
              scheduled_visits: true,
              targets: true,
            },
          },
        },
      });

      return this.transformToResponseDto(user);

    } catch (error) {
      if (error instanceof ConflictException || error instanceof NotFoundException) {
        throw error;
      }
      
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }
      
      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      console.error('Error creating user:', error);
      throw new BadRequestException('Failed to create user');
    }
  }

  /**
   * Retrieves a paginated list of users with search functionality
   * 
   * @param paginationDto - Pagination and search parameters
   * @returns Promise<PaginatedResponseDto<UserResponseDto>> - Paginated list of users
   * @throws BadRequestException if database operation fails
   */
  async findAll(paginationDto: PaginationDto): Promise<PaginatedResponseDto<UserResponseDto>> {
    try {
      const { page = 1, limit = 10, search } = paginationDto;
      const skip = (page - 1) * limit;

      // Build where clause for search functionality
      const whereClause: Prisma.UserWhereInput = search
        ? {
            OR: [
              {
                name: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
              {
                email: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
              {
                rm_code: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
              {
                role: {
                  name: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
              },
              {
                branch: {
                  name: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
              },
            ],
          }
        : {};

      // Execute both count and data queries in parallel for better performance
      const [users, total] = await Promise.all([
        this.prisma.user.findMany({
          where: whereClause,
          skip,
          take: limit,
          include: {
            role: true,
            branch: {
              include: {
                region: true,
              },
            },
            _count: {
              select: {
                general_activities: true,
                leads: true,
                loan_activities: true,
                scheduled_visits: true,
                targets: true,
              },
            },
          },
          orderBy: {
            name: 'asc',
          },
        }),
        this.prisma.user.count({
          where: whereClause,
        }),
      ]);

      // Transform data to response DTOs
      const data = users.map(user => this.transformToResponseDto(user));

      const totalPages = Math.ceil(total / limit);
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      return {
        data,
        meta: {
          total,
          page,
          limit,
          totalPages,
          hasNextPage,
          hasPreviousPage,
        },
      };

    } catch (error) {
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }
      
      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      console.error('Error fetching users:', error);
      throw new BadRequestException('Failed to fetch users');
    }
  }

  /**
   * Retrieves a single user by ID
   * 
   * @param id - User UUID
   * @returns Promise<UserResponseDto> - The user with related data
   * @throws NotFoundException if user doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async findOne(id: string): Promise<UserResponseDto> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
        include: {
          role: true,
          branch: {
            include: {
              region: true,
            },
          },
          _count: {
            select: {
              general_activities: true,
              leads: true,
              loan_activities: true,
              scheduled_visits: true,
              targets: true,
            },
          },
        },
      });

      if (!user) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      return this.transformToResponseDto(user);

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }
      
      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      console.error('Error fetching user:', error);
      throw new BadRequestException('Failed to fetch user');
    }
  }

  /**
   * Updates an existing user
   *
   * @param id - User UUID
   * @param updateUserDto - Data for updating the user
   * @returns Promise<UserResponseDto> - The updated user with related data
   * @throws NotFoundException if user, role, or branch doesn't exist
   * @throws ConflictException if email already exists for another user
   * @throws BadRequestException if database operation fails
   */
  async update(id: string, updateUserDto: UpdateUserDto): Promise<UserResponseDto> {
    try {
      // Check if user exists
      const existingUser = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!existingUser) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      // If email is being updated, check for conflicts
      if (updateUserDto.email && updateUserDto.email !== existingUser.email) {
        const conflictingUser = await this.prisma.user.findFirst({
          where: {
            email: {
              equals: updateUserDto.email,
              mode: 'insensitive',
            },
            id: {
              not: id, // Exclude current user from conflict check
            },
          },
        });

        if (conflictingUser) {
          throw new ConflictException(`User with email '${updateUserDto.email}' already exists`);
        }
      }

      // Validate role if being updated
      if (updateUserDto.role_id) {
        const role = await this.prisma.role.findUnique({
          where: { id: updateUserDto.role_id },
        });

        if (!role) {
          throw new NotFoundException(`Role with ID '${updateUserDto.role_id}' not found`);
        }
      }

      // Validate branch if being updated
      if (updateUserDto.branch_id) {
        const branch = await this.prisma.branch.findUnique({
          where: { id: updateUserDto.branch_id },
        });

        if (!branch) {
          throw new NotFoundException(`Branch with ID '${updateUserDto.branch_id}' not found`);
        }
      }

      // Prepare update data with password hashing if password is being updated
      const updateData = { ...updateUserDto };
      if (updateUserDto.password) {
        updateData.password = await bcrypt.hash(updateUserDto.password, this.saltRounds);
      }

      const updatedUser = await this.prisma.user.update({
        where: { id },
        data: updateData,
        include: {
          role: true,
          branch: {
            include: {
              region: true,
            },
          },
          _count: {
            select: {
              general_activities: true,
              leads: true,
              loan_activities: true,
              scheduled_visits: true,
              targets: true,
            },
          },
        },
      });

      return this.transformToResponseDto(updatedUser);

    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error updating user:', error);
      throw new BadRequestException('Failed to update user');
    }
  }

  /**
   * Deletes a user by ID
   *
   * @param id - User UUID
   * @returns Promise<void>
   * @throws NotFoundException if user doesn't exist
   * @throws ConflictException if user has associated data that prevents deletion
   * @throws BadRequestException if database operation fails
   */
  async remove(id: string): Promise<void> {
    try {
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              general_activities: true,
              leads: true,
              loan_activities: true,
              scheduled_visits: true,
              targets: true,
              hitlist_calls: true,
              hitlist_assignments: true,
              hitlist_uploads: true,
            },
          },
        },
      });

      if (!user) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      // Check if user has associated data that prevents deletion
      const hasAssociatedData =
        user._count.general_activities > 0 ||
        user._count.leads > 0 ||
        user._count.loan_activities > 0 ||
        user._count.scheduled_visits > 0 ||
        user._count.targets > 0 ||
        user._count.hitlist_calls > 0 ||
        user._count.hitlist_assignments > 0 ||
        user._count.hitlist_uploads > 0;

      if (hasAssociatedData) {
        throw new ConflictException(
          `Cannot delete user '${user.name}' because they have associated data. ` +
          `Please reassign or remove their activities, leads, and other data first.`
        );
      }

      await this.prisma.user.delete({
        where: { id },
      });

    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error deleting user:', error);
      throw new BadRequestException('Failed to delete user');
    }
  }

  /**
   * Assigns a role to a user
   *
   * @param id - User UUID
   * @param assignRoleDto - Role assignment data
   * @returns Promise<UserResponseDto> - The updated user with new role
   * @throws NotFoundException if user or role doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async assignRole(id: string, assignRoleDto: AssignRoleDto): Promise<UserResponseDto> {
    try {
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!user) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      // Validate that role exists
      const role = await this.prisma.role.findUnique({
        where: { id: assignRoleDto.role_id },
      });

      if (!role) {
        throw new NotFoundException(`Role with ID '${assignRoleDto.role_id}' not found`);
      }

      const updatedUser = await this.prisma.user.update({
        where: { id },
        data: { role_id: assignRoleDto.role_id },
        include: {
          role: true,
          branch: {
            include: {
              region: true,
            },
          },
          _count: {
            select: {
              general_activities: true,
              leads: true,
              loan_activities: true,
              scheduled_visits: true,
              targets: true,
            },
          },
        },
      });

      return this.transformToResponseDto(updatedUser);

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error assigning role to user:', error);
      throw new BadRequestException('Failed to assign role to user');
    }
  }

  /**
   * Transfers a user to a different branch
   *
   * @param id - User UUID
   * @param transferBranchDto - Branch transfer data
   * @returns Promise<UserResponseDto> - The updated user with new branch
   * @throws NotFoundException if user or branch doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async transferBranch(id: string, transferBranchDto: TransferBranchDto): Promise<UserResponseDto> {
    try {
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!user) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      // Validate that branch exists
      const branch = await this.prisma.branch.findUnique({
        where: { id: transferBranchDto.branch_id },
        include: { region: true },
      });

      if (!branch) {
        throw new NotFoundException(`Branch with ID '${transferBranchDto.branch_id}' not found`);
      }

      const updatedUser = await this.prisma.user.update({
        where: { id },
        data: { branch_id: transferBranchDto.branch_id },
        include: {
          role: true,
          branch: {
            include: {
              region: true,
            },
          },
          _count: {
            select: {
              general_activities: true,
              leads: true,
              loan_activities: true,
              scheduled_visits: true,
              targets: true,
            },
          },
        },
      });

      return this.transformToResponseDto(updatedUser);

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error transferring user to branch:', error);
      throw new BadRequestException('Failed to transfer user to branch');
    }
  }

  /**
   * Updates the last login timestamp for a user
   *
   * @param id - User UUID
   * @param updateLastLoginDto - Last login data
   * @returns Promise<void>
   * @throws NotFoundException if user doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async updateLastLogin(id: string, updateLastLoginDto: UpdateLastLoginDto): Promise<void> {
    try {
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!user) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      await this.prisma.user.update({
        where: { id },
        data: { last_login: updateLastLoginDto.last_login },
      });

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error updating user last login:', error);
      throw new BadRequestException('Failed to update user last login');
    }
  }

  /**
   * Changes a user's password after verifying the current password
   *
   * @param id - User UUID
   * @param changePasswordDto - Password change data
   * @returns Promise<void>
   * @throws NotFoundException if user doesn't exist
   * @throws UnauthorizedException if current password is incorrect
   * @throws BadRequestException if database operation fails
   */
  async changePassword(id: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    try {
      // Check if user exists and get current password
      const user = await this.prisma.user.findUnique({
        where: { id },
        select: { id: true, password: true },
      });

      if (!user) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      // Check if user has a password set
      if (!user.password) {
        throw new UnauthorizedException('User does not have a password set. Please contact an administrator.');
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(changePasswordDto.currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        throw new UnauthorizedException('Current password is incorrect');
      }

      // Hash new password and update
      const hashedNewPassword = await bcrypt.hash(changePasswordDto.newPassword, this.saltRounds);

      await this.prisma.user.update({
        where: { id },
        data: { password: hashedNewPassword },
      });

    } catch (error) {
      if (error instanceof NotFoundException || error instanceof UnauthorizedException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error changing user password:', error);
      throw new BadRequestException('Failed to change password');
    }
  }

  /**
   * Resets a user's password (admin operation, no current password verification)
   *
   * @param id - User UUID
   * @param resetPasswordDto - Password reset data
   * @returns Promise<void>
   * @throws NotFoundException if user doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async resetPassword(id: string, resetPasswordDto: ResetPasswordDto): Promise<void> {
    try {
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id },
        select: { id: true },
      });

      if (!user) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      // Hash new password and update
      const hashedNewPassword = await bcrypt.hash(resetPasswordDto.newPassword, this.saltRounds);

      await this.prisma.user.update({
        where: { id },
        data: { password: hashedNewPassword },
      });

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error resetting user password:', error);
      throw new BadRequestException('Failed to reset password');
    }
  }

  /**
   * Verifies a user's password (for authentication)
   *
   * @param email - User email
   * @param password - Plain text password
   * @returns Promise<UserResponseDto | null> - User data if password is correct, null otherwise
   * @throws BadRequestException if database operation fails
   */
  async verifyPassword(email: string, password: string): Promise<UserResponseDto | null> {
    try {
      const user = await this.prisma.user.findFirst({
        where: {
          email: {
            equals: email,
            mode: 'insensitive',
          },
        },
        include: {
          role: true,
          branch: {
            include: {
              region: true,
            },
          },
          _count: {
            select: {
              general_activities: true,
              leads: true,
              loan_activities: true,
              scheduled_visits: true,
              targets: true,
            },
          },
        },
      });

      if (!user) {
        return null;
      }

      // Check if user has a password set
      if (!user.password) {
        return null;
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        return null;
      }

      return this.transformToResponseDto(user);

    } catch (error) {
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error verifying user password:', error);
      throw new BadRequestException('Failed to verify password');
    }
  }

  /**
   * Transforms a Prisma user object to UserResponseDto
   *
   * @param user - Prisma user object with includes
   * @returns UserResponseDto - Transformed response object
   */
  private transformToResponseDto(user: any): UserResponseDto {
    return {
      id: user.id,
      name: user.name,
      email: user.email,
      phone_number: user.phone_number,
      rm_code: user.rm_code,
      last_login: user.last_login,
      created_at: user.created_at,
      updated_at: user.updated_at,
      role: {
        id: user.role.id,
        name: user.role.name,
        description: user.role.description,
      },
      branch: {
        id: user.branch.id,
        name: user.branch.name,
        region: {
          id: user.branch.region.id,
          name: user.branch.region.name,
        },
      },
      generalActivitiesCount: user._count.general_activities,
      leadsCount: user._count.leads,
      loanActivitiesCount: user._count.loan_activities,
      scheduledVisitsCount: user._count.scheduled_visits,
      targetsCount: user._count.targets,
    };
  }
}
