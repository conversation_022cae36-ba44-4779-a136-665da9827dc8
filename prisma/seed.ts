import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // First, let's create some regions if they don't exist
  const existingRegions = await prisma.region.findMany();

  let centralRegion, northernRegion;

  if (existingRegions.length === 0) {
    console.log('📍 Creating regions...');

    centralRegion = await prisma.region.create({
      data: {
        name: 'Central Region',
      },
    });

    northernRegion = await prisma.region.create({
      data: {
        name: 'Northern Region',
      },
    });

    console.log(
      `✅ Created regions: ${centralRegion.name}, ${northernRegion.name}`,
    );
  } else {
    // Use existing regions
    centralRegion = existingRegions[0];
    northernRegion =
      existingRegions.length > 1 ? existingRegions[1] : existingRegions[0];
    console.log(
      `📍 Using existing regions: ${centralRegion.name}, ${northernRegion.name}`,
    );
  }

  // Check if branches already exist
  const existingBranches = await prisma.branch.findMany();

  if (existingBranches.length === 0) {
    console.log('🏢 Creating test branches...');

    const branch1 = await prisma.branch.create({
      data: {
        name: 'Main Branch Downtown',
        region_id: centralRegion.id,
      },
    });

    const branch2 = await prisma.branch.create({
      data: {
        name: 'Northside Branch',
        region_id: northernRegion.id,
      },
    });

    console.log(`✅ Created branches: ${branch1.name}, ${branch2.name}`);
  } else {
    console.log(`🏢 Branches already exist (${existingBranches.length} found)`);
  }

  // Check if customer categories already exist
  const existingCustomerCategories = await prisma.customerCategory.findMany();

  if (existingCustomerCategories.length === 0) {
    console.log('🏷️ Creating test customer categories...');

    const category1 = await prisma.customerCategory.create({
      data: {
        name: 'Corporate',
      },
    });

    const category2 = await prisma.customerCategory.create({
      data: {
        name: 'Small Business',
      },
    });

    const category3 = await prisma.customerCategory.create({
      data: {
        name: 'Individual',
      },
    });

    console.log(
      `✅ Created customer categories: ${category1.name}, ${category2.name}, ${category3.name}`,
    );
  } else {
    console.log(
      `🏷️ Customer categories already exist (${existingCustomerCategories.length} found)`,
    );
  }

  // Check if employers already exist
  const existingEmployers = await prisma.employer.findMany();

  if (existingEmployers.length === 0) {
    console.log('🏭 Creating test employers...');

    const employer1 = await prisma.employer.create({
      data: {
        name: 'Tech Solutions Ltd',
      },
    });

    const employer2 = await prisma.employer.create({
      data: {
        name: 'Manufacturing Corp',
      },
    });

    const employer3 = await prisma.employer.create({
      data: {
        name: 'Healthcare Services Inc',
      },
    });

    console.log(
      `✅ Created employers: ${employer1.name}, ${employer2.name}, ${employer3.name}`,
    );
  } else {
    console.log(
      `🏭 Employers already exist (${existingEmployers.length} found)`,
    );
  }

  // Check if users already exist
  const existingUsers = await prisma.user.findMany();

  if (existingUsers.length === 0) {
    console.log('👥 Creating test users...');

    // First, we need to create some roles
    const existingRoles = await prisma.role.findMany();
    let rmRole;

    if (existingRoles.length === 0) {
      rmRole = await prisma.role.create({
        data: {
          name: 'Relationship Manager',
          description: 'Manages customer relationships and leads',
        },
      });
      console.log(`✅ Created role: ${rmRole.name}`);
    } else {
      rmRole = existingRoles[0];
    }

    // Get the branches to assign users to
    const branches = await prisma.branch.findMany();
    const branch1 = branches[0];
    const branch2 = branches.length > 1 ? branches[1] : branches[0];

    const user1 = await prisma.user.create({
      data: {
        name: 'John Smith',
        email: '<EMAIL>',
        password: 'defaultPassword123',
        phone_number: '+254712345678',
        rm_code: 'RM001',
        role_id: rmRole.id,
        branch_id: branch1.id,
      },
    });

    const user2 = await prisma.user.create({
      data: {
        name: 'Jane Doe',
        email: '<EMAIL>',
        password: 'defaultPassword123',
        phone_number: '+254787654321',
        rm_code: 'RM002',
        role_id: rmRole.id,
        branch_id: branch2.id,
      },
    });

    console.log(`✅ Created users: ${user1.name}, ${user2.name}`);
  } else {
    console.log(`👥 Users already exist (${existingUsers.length} found)`);
  }

  // Check if ISIC sectors already exist
  const existingIsicSectors = await prisma.iSICSector.findMany();

  if (existingIsicSectors.length === 0) {
    console.log('🏭 Creating test ISIC sectors...');

    const sector1 = await prisma.iSICSector.create({
      data: {
        code: 'A01',
        name: 'Agriculture and Forestry',
      },
    });

    const sector2 = await prisma.iSICSector.create({
      data: {
        code: 'C10',
        name: 'Manufacturing',
      },
    });

    const sector3 = await prisma.iSICSector.create({
      data: {
        code: 'J62',
        name: 'Information Technology',
      },
    });

    console.log(
      `✅ Created ISIC sectors: ${sector1.name}, ${sector2.name}, ${sector3.name}`,
    );
  } else {
    console.log(
      `🏭 ISIC sectors already exist (${existingIsicSectors.length} found)`,
    );
  }

  console.log('🎉 Database seeding completed!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
