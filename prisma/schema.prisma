generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                  String            @id @default(uuid())
  name                String
  email               String
  password            String?
  phone_number        String?
  rm_code             String
  role_id             String
  branch_id           String
  last_login          DateTime?
  created_at          DateTime          @default(now())
  updated_at          DateTime          @updatedAt
  activities          Activity[]
  general_activities  GeneralActivity[]
  hitlist_calls       HitlistCallLog[]
  hitlist_assignments HitlistEntry[]    @relation("HitlistAssignedTo")
  hitlist_uploads     HitlistEntry[]    @relation("HitlistUploadedBy")
  leads               Lead[]
  loan_activities     LoanActivity[]
  scheduled_visits    ScheduledVisit[]
  targets             Target[]
  branch              Branch            @relation(fields: [branch_id], references: [id])
  role                Role              @relation(fields: [role_id], references: [id])

  @@map("users")
}

model Role {
  id               String           @id @default(uuid())
  name             String
  description      String?
  role_permissions RolePermission[]
  users            User[]

  @@map("roles")
}

model Permission {
  id               String           @id
  name             String
  description      String?
  role_permissions RolePermission[]

  @@map("permissions")
}

model RolePermission {
  role_id       String
  permission_id String
  created_at    DateTime?  @default(now())
  permission    Permission @relation(fields: [permission_id], references: [id])
  role          Role       @relation(fields: [role_id], references: [id])

  @@id([role_id, permission_id])
  @@map("role_permissions")
}

model Region {
  id         String    @id @default(uuid())
  name       String
  created_at DateTime? @default(now())
  updated_at DateTime? @updatedAt
  branches   Branch[]

  @@map("regions")
}

model Branch {
  id        String @id @default(uuid())
  name      String
  region_id String
  region    Region @relation(fields: [region_id], references: [id])
  leads     Lead[]
  users     User[]

  @@map("branches")
}

model CustomerCategory {
  id    String @id @default(uuid())
  name  String
  leads Lead[]

  @@map("customer_categories")
}

model ISICSector {
  id         String   @id @default(uuid())
  code       String?
  name       String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  leads      Lead[]

  @@map("isic_sectors")
}

model Employer {
  id    String @id @default(uuid())
  name  String
  leads Lead[]

  @@map("employers")
}

model AnchorRelationship {
  id    String @id @default(uuid())
  name  String
  leads Lead[]

  @@map("anchor_relationships")
}

model Lead {
  id                      String               @id @default(uuid())
  customer_name           String?
  parent_lead_id          String?
  customer_category_id    String?
  isic_sector_id          String?
  phone_number            String?
  type_of_lead            String?
  branch_id               String?
  rm_user_id              String?
  client_id               String?              @unique
  account_number          String?              @unique
  employer_id             String?
  anchor_relationship_id  String?
  lead_status             String?
  activities              Activity[]
  general_activities      GeneralActivity[]
  hitlist_entries         HitlistEntry[]
  contact_persons         LeadContactPerson[]
  anchor_relationship     AnchorRelationship?  @relation(fields: [anchor_relationship_id], references: [id])
  branch                  Branch?              @relation(fields: [branch_id], references: [id])
  customer_category       CustomerCategory?    @relation(fields: [customer_category_id], references: [id])
  employer                Employer?            @relation(fields: [employer_id], references: [id])
  isic_sector             ISICSector?          @relation(fields: [isic_sector_id], references: [id])
  leads                   Lead?                @relation("leadsToleads", fields: [parent_lead_id], references: [id])
  other_leads             Lead[]               @relation("leadsToleads")
  rm_user                 User?                @relation(fields: [rm_user_id], references: [id])
  loan_activities         LoanActivity[]
  scheduled_visits        ScheduledVisit[]

  @@map("leads")
}

model LeadContactPerson {
  id           String @id @default(uuid())
  lead_id      String
  name         String
  phone_number String
  lead         Lead   @relation(fields: [lead_id], references: [id])

  @@map("lead_contact_persons")
}

model GeneralActivity {
  id                   String               @id @default(uuid())
  client_id            String?
  activity_type        String?
  purpose_id           String
  activity_date        DateTime?
  via_api              Boolean?
  api_call_reference   String?
  call_status          String?
  comment              String?
  next_visit_date      DateTime?
  status_id            String
  business_segment     String?
  performed_by_user_id String
  attachments          ActivityAttachment[]
  lead                 Lead?                @relation(fields: [client_id], references: [client_id])
  performed_by         User                 @relation(fields: [performed_by_user_id], references: [id])
  purpose              PurposeOfActivity    @relation(fields: [purpose_id], references: [id])
  status               VisitStatus          @relation(fields: [status_id], references: [id])

  @@map("general_activities")
}

model ActivityAttachment {
  id                  String           @id @default(uuid())
  general_activity_id String?
  activity_id         String?
  file_url            String?
  general_activity    GeneralActivity? @relation(fields: [general_activity_id], references: [id])
  activity            Activity?        @relation(fields: [activity_id], references: [id])

  @@map("activity_attachments")
}

model PurposeOfActivity {
  id                 String            @id @default(uuid())
  name               String?
  description        String?
  general_activities GeneralActivity[]
  activities         Activity[]

  @@map("purpose_of_activities")
}

model VisitStatus {
  id                 String            @id @default(uuid())
  status             String?
  description        String?
  general_activities GeneralActivity[]

  @@map("visit_statuses")
}

model HitlistEntry {
  id                   String           @id @default(uuid())
  uploaded_at          DateTime?
  uploaded_by_user_id  String
  hitlist_type         String?
  file_reference       String?
  client_id            String?
  account_type         String?
  account_open_date    DateTime?
  planned_contact_date DateTime?
  status               String?
  assigned_to_user_id  String
  call_logs            HitlistCallLog[]
  assigned_to          User             @relation("HitlistAssignedTo", fields: [assigned_to_user_id], references: [id])
  lead                 Lead?            @relation(fields: [client_id], references: [client_id])
  uploaded_by          User             @relation("HitlistUploadedBy", fields: [uploaded_by_user_id], references: [id])

  @@map("hitlist_entries")
}

model HitlistFeedbackCategory {
  id        String           @id @default(uuid())
  name      String?
  call_logs HitlistCallLog[]

  @@map("hitlist_feedback_categories")
}

model HitlistCallLog {
  id                   String                  @id @default(uuid())
  hitlist_entry_id     String
  call_date            DateTime?
  called_by_user_id    String
  via_api              Boolean?
  api_call_reference   String?
  feedback_category_id String
  comment              String?
  called_by            User                    @relation(fields: [called_by_user_id], references: [id])
  feedback_category    HitlistFeedbackCategory @relation(fields: [feedback_category_id], references: [id])
  hitlist_entry        HitlistEntry            @relation(fields: [hitlist_entry_id], references: [id])

  @@map("hitlist_call_logs")
}

model LoanActivity {
  id                  String    @id @default(uuid())
  client_id           String?
  loan_account_number String?
  purpose             String?
  loan_balance        Decimal?
  arrears_days        Int?
  comment             String?
  rm_user_id          String
  activity_date       DateTime?
  via_api             Boolean?
  api_call_reference  String?
  attachment          String?
  lead                Lead?     @relation(fields: [client_id], references: [client_id])
  rm_user             User      @relation(fields: [rm_user_id], references: [id])

  @@map("loan_activities")
}

model ScheduledVisit {
  id                 String    @id @default(uuid())
  lead_id            String
  scheduled_by       String
  scheduled_for      DateTime?
  status             String?
  via_api            Boolean?
  api_call_reference String?
  lead               Lead      @relation(fields: [lead_id], references: [id])
  scheduled_by_user  User      @relation(fields: [scheduled_by], references: [id])

  @@map("scheduled_visits")
}

model Target {
  id             String    @id @default(uuid())
  user_id        String
  target_type    String?
  monthly_target Int?
  period_start   DateTime?
  period_end     DateTime?
  user           User      @relation(fields: [user_id], references: [id])

  @@map("targets")
}

model Activity {
  id                    String               @id @default(uuid())
  lead_id               String
  activity_type         String
  interaction_type      String?
  call_status           String?
  visit_status          String?
  notes                 String?
  call_duration_minutes Int?
  next_followup_date    DateTime?
  performed_by_user_id  String
  purpose_id            String?
  created_at            DateTime             @default(now())
  updated_at            DateTime             @updatedAt
  attachments           ActivityAttachment[]
  lead                  Lead                 @relation(fields: [lead_id], references: [id])
  performed_by          User                 @relation(fields: [performed_by_user_id], references: [id])
  purpose               PurposeOfActivity?   @relation(fields: [purpose_id], references: [id])

  @@map("activities")
}
