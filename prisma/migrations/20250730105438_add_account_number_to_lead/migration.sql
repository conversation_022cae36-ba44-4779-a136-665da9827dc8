/*
  Warnings:

  - A unique constraint covering the columns `[account_number]` on the table `leads` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "activity_attachments" DROP CONSTRAINT "activity_attachments_activity_id_fkey";

-- DropForeignKey
ALTER TABLE "activity_attachments" DROP CONSTRAINT "activity_attachments_general_activity_id_fkey";

-- AlterTable
ALTER TABLE "leads" ADD COLUMN     "account_number" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "leads_account_number_key" ON "leads"("account_number");

-- AddForeignKey
ALTER TABLE "activity_attachments" ADD CONSTRAINT "activity_attachments_general_activity_id_fkey" FOREIGN KEY ("general_activity_id") REFERENCES "general_activities"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeign<PERSON>ey
ALTER TABLE "activity_attachments" ADD CONSTRAINT "activity_attachments_activity_id_fkey" FOREIGN KEY ("activity_id") REFERENCES "activities"("id") ON DELETE SET NULL ON UPDATE CASCADE;
