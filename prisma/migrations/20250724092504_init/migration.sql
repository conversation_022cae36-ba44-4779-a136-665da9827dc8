-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "rm_code" TEXT NOT NULL,
    "role_id" TEXT NOT NULL,
    "branch_id" TEXT NOT NULL,
    "last_login" TIMESTAMP(3),

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "roles" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "description" TEXT,

    CONSTRAINT "roles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "permissions" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "description" TEXT,

    CONSTRAINT "permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "role_permissions" (
    "role_id" TEXT NOT NULL,
    "permission_id" TEXT NOT NULL,

    CONSTRAINT "role_permissions_pkey" PRIMARY KEY ("role_id","permission_id")
);

-- CreateTable
CREATE TABLE "regions" (
    "id" TEXT NOT NULL,
    "name" TEXT,

    CONSTRAINT "regions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "branches" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "region_id" TEXT NOT NULL,

    CONSTRAINT "branches_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_categories" (
    "id" TEXT NOT NULL,
    "name" TEXT,

    CONSTRAINT "customer_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "isic_sectors" (
    "id" TEXT NOT NULL,
    "code" TEXT,
    "name" TEXT,

    CONSTRAINT "isic_sectors_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "employers" (
    "id" TEXT NOT NULL,
    "name" TEXT,

    CONSTRAINT "employers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "leads" (
    "id" TEXT NOT NULL,
    "customer_name" TEXT,
    "parent_lead_id" TEXT,
    "customer_category_id" TEXT NOT NULL,
    "isic_sector_id" TEXT NOT NULL,
    "phone_number" TEXT,
    "type_of_lead" TEXT,
    "branch_id" TEXT NOT NULL,
    "rm_user_id" TEXT NOT NULL,
    "client_id" TEXT,
    "employer_id" TEXT NOT NULL,

    CONSTRAINT "leads_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "lead_contact_persons" (
    "id" TEXT NOT NULL,
    "lead_id" TEXT NOT NULL,
    "name" TEXT,
    "phone_number" TEXT,

    CONSTRAINT "lead_contact_persons_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "general_activities" (
    "id" TEXT NOT NULL,
    "client_id" TEXT,
    "activity_type" TEXT,
    "purpose_id" TEXT NOT NULL,
    "activity_date" TIMESTAMP(3),
    "via_api" BOOLEAN,
    "api_call_reference" TEXT,
    "call_status" TEXT,
    "comment" TEXT,
    "next_visit_date" TIMESTAMP(3),
    "status_id" TEXT NOT NULL,
    "business_segment" TEXT,
    "performed_by_user_id" TEXT NOT NULL,

    CONSTRAINT "general_activities_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "activity_attachments" (
    "id" TEXT NOT NULL,
    "general_activity_id" TEXT NOT NULL,
    "file_url" TEXT,

    CONSTRAINT "activity_attachments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "purpose_of_activities" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "description" TEXT,

    CONSTRAINT "purpose_of_activities_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "visit_statuses" (
    "id" TEXT NOT NULL,
    "status" TEXT,
    "description" TEXT,

    CONSTRAINT "visit_statuses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "hitlist_entries" (
    "id" TEXT NOT NULL,
    "uploaded_at" TIMESTAMP(3),
    "uploaded_by_user_id" TEXT NOT NULL,
    "hitlist_type" TEXT,
    "file_reference" TEXT,
    "client_id" TEXT,
    "account_type" TEXT,
    "account_open_date" TIMESTAMP(3),
    "planned_contact_date" TIMESTAMP(3),
    "status" TEXT,
    "assigned_to_user_id" TEXT NOT NULL,

    CONSTRAINT "hitlist_entries_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "hitlist_feedback_categories" (
    "id" TEXT NOT NULL,
    "name" TEXT,

    CONSTRAINT "hitlist_feedback_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "hitlist_call_logs" (
    "id" TEXT NOT NULL,
    "hitlist_entry_id" TEXT NOT NULL,
    "call_date" TIMESTAMP(3),
    "called_by_user_id" TEXT NOT NULL,
    "via_api" BOOLEAN,
    "api_call_reference" TEXT,
    "feedback_category_id" TEXT NOT NULL,
    "comment" TEXT,

    CONSTRAINT "hitlist_call_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "loan_activities" (
    "id" TEXT NOT NULL,
    "client_id" TEXT,
    "loan_account_number" TEXT,
    "purpose" TEXT,
    "loan_balance" DECIMAL(65,30),
    "arrears_days" INTEGER,
    "comment" TEXT,
    "rm_user_id" TEXT NOT NULL,
    "activity_date" TIMESTAMP(3),
    "via_api" BOOLEAN,
    "api_call_reference" TEXT,
    "attachment" TEXT,

    CONSTRAINT "loan_activities_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "scheduled_visits" (
    "id" TEXT NOT NULL,
    "lead_id" TEXT NOT NULL,
    "scheduled_by" TEXT NOT NULL,
    "scheduled_for" TIMESTAMP(3),
    "status" TEXT,
    "via_api" BOOLEAN,
    "api_call_reference" TEXT,

    CONSTRAINT "scheduled_visits_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "targets" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "target_type" TEXT,
    "monthly_target" INTEGER,
    "period_start" TIMESTAMP(3),
    "period_end" TIMESTAMP(3),

    CONSTRAINT "targets_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "leads_client_id_key" ON "leads"("client_id");

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_branch_id_fkey" FOREIGN KEY ("branch_id") REFERENCES "branches"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "permissions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "branches" ADD CONSTRAINT "branches_region_id_fkey" FOREIGN KEY ("region_id") REFERENCES "regions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_parent_lead_id_fkey" FOREIGN KEY ("parent_lead_id") REFERENCES "leads"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_customer_category_id_fkey" FOREIGN KEY ("customer_category_id") REFERENCES "customer_categories"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_isic_sector_id_fkey" FOREIGN KEY ("isic_sector_id") REFERENCES "isic_sectors"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_branch_id_fkey" FOREIGN KEY ("branch_id") REFERENCES "branches"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_rm_user_id_fkey" FOREIGN KEY ("rm_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_employer_id_fkey" FOREIGN KEY ("employer_id") REFERENCES "employers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lead_contact_persons" ADD CONSTRAINT "lead_contact_persons_lead_id_fkey" FOREIGN KEY ("lead_id") REFERENCES "leads"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "general_activities" ADD CONSTRAINT "general_activities_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "leads"("client_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "general_activities" ADD CONSTRAINT "general_activities_purpose_id_fkey" FOREIGN KEY ("purpose_id") REFERENCES "purpose_of_activities"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "general_activities" ADD CONSTRAINT "general_activities_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "visit_statuses"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "general_activities" ADD CONSTRAINT "general_activities_performed_by_user_id_fkey" FOREIGN KEY ("performed_by_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activity_attachments" ADD CONSTRAINT "activity_attachments_general_activity_id_fkey" FOREIGN KEY ("general_activity_id") REFERENCES "general_activities"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "hitlist_entries" ADD CONSTRAINT "hitlist_entries_uploaded_by_user_id_fkey" FOREIGN KEY ("uploaded_by_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "hitlist_entries" ADD CONSTRAINT "hitlist_entries_assigned_to_user_id_fkey" FOREIGN KEY ("assigned_to_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "hitlist_entries" ADD CONSTRAINT "hitlist_entries_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "leads"("client_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "hitlist_call_logs" ADD CONSTRAINT "hitlist_call_logs_hitlist_entry_id_fkey" FOREIGN KEY ("hitlist_entry_id") REFERENCES "hitlist_entries"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "hitlist_call_logs" ADD CONSTRAINT "hitlist_call_logs_called_by_user_id_fkey" FOREIGN KEY ("called_by_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "hitlist_call_logs" ADD CONSTRAINT "hitlist_call_logs_feedback_category_id_fkey" FOREIGN KEY ("feedback_category_id") REFERENCES "hitlist_feedback_categories"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "loan_activities" ADD CONSTRAINT "loan_activities_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "leads"("client_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "loan_activities" ADD CONSTRAINT "loan_activities_rm_user_id_fkey" FOREIGN KEY ("rm_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "scheduled_visits" ADD CONSTRAINT "scheduled_visits_lead_id_fkey" FOREIGN KEY ("lead_id") REFERENCES "leads"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "scheduled_visits" ADD CONSTRAINT "scheduled_visits_scheduled_by_fkey" FOREIGN KEY ("scheduled_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "targets" ADD CONSTRAINT "targets_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
