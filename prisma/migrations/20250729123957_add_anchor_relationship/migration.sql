-- AlterTable
ALTER TABLE "leads" ADD COLUMN     "anchor_relationship_id" TEXT,
ADD COLUMN     "lead_status" TEXT;

-- AlterTable
ALTER TABLE "users" ALTER COLUMN "phone_number" DROP NOT NULL;

-- CreateTable
CREATE TABLE "anchor_relationships" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "anchor_relationships_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_anchor_relationship_id_fkey" FOREIGN KEY ("anchor_relationship_id") REFERENCES "anchor_relationships"("id") ON DELETE SET NULL ON UPDATE CASCADE;
