/*
  Warnings:

  - You are about to drop the column `created_at` on the `branches` table. All the data in the column will be lost.
  - You are about to drop the column `monthly_target` on the `targets` table. All the data in the column will be lost.
  - You are about to drop the column `period_end` on the `targets` table. All the data in the column will be lost.
  - You are about to drop the column `period_start` on the `targets` table. All the data in the column will be lost.
  - You are about to drop the column `target_type` on the `targets` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[account_number]` on the table `leads` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `updated_at` to the `isic_sectors` table without a default value. This is not possible if the table is not empty.
  - Made the column `name` on table `permissions` required. This step will fail if there are existing NULL values in that column.
  - Made the column `name` on table `roles` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `end_date` to the `targets` table without a default value. This is not possible if the table is not empty.
  - Added the required column `frequency` to the `targets` table without a default value. This is not possible if the table is not empty.
  - Added the required column `metric_type` to the `targets` table without a default value. This is not possible if the table is not empty.
  - Added the required column `start_date` to the `targets` table without a default value. This is not possible if the table is not empty.
  - Added the required column `target_value` to the `targets` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `targets` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `users` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "MetricType" AS ENUM ('Call', 'Visit');

-- CreateEnum
CREATE TYPE "Frequency" AS ENUM ('daily', 'weekly', 'custom');

-- DropForeignKey
ALTER TABLE "activity_attachments" DROP CONSTRAINT "activity_attachments_general_activity_id_fkey";

-- DropForeignKey
ALTER TABLE "leads" DROP CONSTRAINT "leads_branch_id_fkey";

-- DropForeignKey
ALTER TABLE "leads" DROP CONSTRAINT "leads_customer_category_id_fkey";

-- DropForeignKey
ALTER TABLE "leads" DROP CONSTRAINT "leads_employer_id_fkey";

-- DropForeignKey
ALTER TABLE "leads" DROP CONSTRAINT "leads_isic_sector_id_fkey";

-- DropForeignKey
ALTER TABLE "leads" DROP CONSTRAINT "leads_rm_user_id_fkey";

-- DropForeignKey
ALTER TABLE "targets" DROP CONSTRAINT "targets_user_id_fkey";

-- AlterTable
ALTER TABLE "activity_attachments" ADD COLUMN     "activity_id" TEXT,
ALTER COLUMN "general_activity_id" DROP NOT NULL;

-- AlterTable
ALTER TABLE "branches" DROP COLUMN "created_at";

-- AlterTable
ALTER TABLE "isic_sectors" ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "leads" ADD COLUMN     "account_number" TEXT,
ADD COLUMN     "anchor_relationship_id" TEXT,
ADD COLUMN     "lead_status" TEXT,
ALTER COLUMN "customer_name" DROP NOT NULL,
ALTER COLUMN "customer_category_id" DROP NOT NULL,
ALTER COLUMN "isic_sector_id" DROP NOT NULL,
ALTER COLUMN "phone_number" DROP NOT NULL,
ALTER COLUMN "type_of_lead" DROP NOT NULL,
ALTER COLUMN "branch_id" DROP NOT NULL,
ALTER COLUMN "rm_user_id" DROP NOT NULL,
ALTER COLUMN "employer_id" DROP NOT NULL;

-- AlterTable
ALTER TABLE "permissions" ALTER COLUMN "name" SET NOT NULL;

-- AlterTable
ALTER TABLE "regions" ADD COLUMN     "updated_at" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "role_permissions" ADD COLUMN     "created_at" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "roles" ALTER COLUMN "name" SET NOT NULL;

-- AlterTable
ALTER TABLE "targets" DROP COLUMN "monthly_target",
DROP COLUMN "period_end",
DROP COLUMN "period_start",
DROP COLUMN "target_type",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "end_date" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "frequency" "Frequency" NOT NULL,
ADD COLUMN     "metric_type" "MetricType" NOT NULL,
ADD COLUMN     "role_id" TEXT,
ADD COLUMN     "start_date" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "target_value" INTEGER NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL,
ALTER COLUMN "user_id" DROP NOT NULL;

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "password" TEXT,
ADD COLUMN     "phone_number" TEXT,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- CreateTable
CREATE TABLE "anchor_relationships" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "anchor_relationships_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "activities" (
    "id" TEXT NOT NULL,
    "lead_id" TEXT NOT NULL,
    "activity_type" TEXT NOT NULL,
    "interaction_type" TEXT,
    "call_status" TEXT,
    "visit_status" TEXT,
    "notes" TEXT,
    "call_duration_minutes" INTEGER,
    "next_followup_date" TIMESTAMP(3),
    "performed_by_user_id" TEXT NOT NULL,
    "purpose_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "activities_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "leads_account_number_key" ON "leads"("account_number");

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_anchor_relationship_id_fkey" FOREIGN KEY ("anchor_relationship_id") REFERENCES "anchor_relationships"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_branch_id_fkey" FOREIGN KEY ("branch_id") REFERENCES "branches"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_customer_category_id_fkey" FOREIGN KEY ("customer_category_id") REFERENCES "customer_categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_employer_id_fkey" FOREIGN KEY ("employer_id") REFERENCES "employers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_isic_sector_id_fkey" FOREIGN KEY ("isic_sector_id") REFERENCES "isic_sectors"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_rm_user_id_fkey" FOREIGN KEY ("rm_user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activity_attachments" ADD CONSTRAINT "activity_attachments_general_activity_id_fkey" FOREIGN KEY ("general_activity_id") REFERENCES "general_activities"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activity_attachments" ADD CONSTRAINT "activity_attachments_activity_id_fkey" FOREIGN KEY ("activity_id") REFERENCES "activities"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "targets" ADD CONSTRAINT "targets_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "targets" ADD CONSTRAINT "targets_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activities" ADD CONSTRAINT "activities_lead_id_fkey" FOREIGN KEY ("lead_id") REFERENCES "leads"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activities" ADD CONSTRAINT "activities_performed_by_user_id_fkey" FOREIGN KEY ("performed_by_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activities" ADD CONSTRAINT "activities_purpose_id_fkey" FOREIGN KEY ("purpose_id") REFERENCES "purpose_of_activities"("id") ON DELETE SET NULL ON UPDATE CASCADE;
