/*
  Warnings:

  - Made the column `name` on table `branches` required. This step will fail if there are existing NULL values in that column.
  - Made the column `name` on table `customer_categories` required. This step will fail if there are existing NULL values in that column.
  - Made the column `name` on table `employers` required. This step will fail if there are existing NULL values in that column.
  - Made the column `name` on table `isic_sectors` required. This step will fail if there are existing NULL values in that column.
  - Made the column `name` on table `lead_contact_persons` required. This step will fail if there are existing NULL values in that column.
  - Made the column `phone_number` on table `lead_contact_persons` required. This step will fail if there are existing NULL values in that column.
  - Made the column `customer_name` on table `leads` required. This step will fail if there are existing NULL values in that column.
  - Made the column `phone_number` on table `leads` required. This step will fail if there are existing NULL values in that column.
  - Made the column `type_of_lead` on table `leads` required. This step will fail if there are existing NULL values in that column.
  - Made the column `name` on table `regions` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "branches" ALTER COLUMN "name" SET NOT NULL;

-- AlterTable
ALTER TABLE "customer_categories" ALTER COLUMN "name" SET NOT NULL;

-- AlterTable
ALTER TABLE "employers" ALTER COLUMN "name" SET NOT NULL;

-- AlterTable
ALTER TABLE "isic_sectors" ALTER COLUMN "name" SET NOT NULL;

-- AlterTable
ALTER TABLE "lead_contact_persons" ALTER COLUMN "name" SET NOT NULL,
ALTER COLUMN "phone_number" SET NOT NULL;

-- AlterTable
ALTER TABLE "leads" ALTER COLUMN "customer_name" SET NOT NULL,
ALTER COLUMN "phone_number" SET NOT NULL,
ALTER COLUMN "type_of_lead" SET NOT NULL;

-- AlterTable
ALTER TABLE "regions" ALTER COLUMN "name" SET NOT NULL;
