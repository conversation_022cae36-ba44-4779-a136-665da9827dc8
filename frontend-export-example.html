<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leads Export - Frontend Example</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .export-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .export-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        .export-button:hover {
            transform: translateY(-2px);
        }
        .export-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .search-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 14px;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Leads Export Functionality</h1>
        
        <div class="export-section">
            <h3>🚀 Quick Export</h3>
            <p>Export all leads to Excel file with one click:</p>
            <button class="export-button" onclick="exportAllLeads()">
                📥 Export All Leads
            </button>
            <div id="quickStatus"></div>
        </div>

        <div class="export-section">
            <h3>🔍 Filtered Export</h3>
            <p>Search and export specific leads:</p>
            <input type="text" class="search-input" id="searchInput" 
                   placeholder="Enter search term (e.g., 'corporate', 'john', '+254700000001')" />
            <button class="export-button" onclick="exportFilteredLeads()">
                📥 Export Filtered Leads
            </button>
            <div id="filteredStatus"></div>
        </div>

        <div class="export-section">
            <h3>📋 Features</h3>
            <ul class="feature-list">
                <li>Automatic file download with proper filename</li>
                <li>Comprehensive lead data with relationships</li>
                <li>Search filtering support</li>
                <li>Professional Excel formatting</li>
                <li>Activity counts and last interaction dates</li>
                <li>Export summary information</li>
                <li>Browser-compatible download headers</li>
            </ul>
        </div>

        <div class="export-section">
            <h3>💻 Implementation Code</h3>
            <p>Here's how to implement this in your frontend:</p>
            
            <h4>JavaScript (Vanilla):</h4>
            <div class="code-block">
async function exportLeads(search = '') {
    try {
        const url = search 
            ? `/api/v1/leads/export?search=${encodeURIComponent(search)}`
            : '/api/v1/leads/export';
            
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            }
        });

        if (!response.ok) {
            throw new Error(`Export failed: ${response.statusText}`);
        }

        // Get filename from Content-Disposition header
        const contentDisposition = response.headers.get('Content-Disposition');
        const filename = contentDisposition
            ? contentDisposition.split('filename=')[1].replace(/"/g, '')
            : 'leads-export.xlsx';

        // Create blob and download
        const blob = await response.blob();
        const url2 = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url2;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url2);

        return { success: true, filename };
    } catch (error) {
        return { success: false, error: error.message };
    }
}
            </div>

            <h4>React Example:</h4>
            <div class="code-block">
const ExportButton = ({ search = '' }) => {
    const [loading, setLoading] = useState(false);
    
    const handleExport = async () => {
        setLoading(true);
        try {
            const result = await exportLeads(search);
            if (result.success) {
                toast.success(`Exported to ${result.filename}`);
            } else {
                toast.error(result.error);
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        &lt;button onClick={handleExport} disabled={loading}&gt;
            {loading ? 'Exporting...' : '📥 Export Leads'}
        &lt;/button&gt;
    );
};
            </div>
        </div>

        <div class="export-section">
            <h3>🔧 API Details</h3>
            <p><strong>Endpoint:</strong> <code>GET /api/v1/leads/export</code></p>
            <p><strong>Query Parameters:</strong></p>
            <ul>
                <li><code>search</code> (optional) - Filter leads by search term</li>
            </ul>
            <p><strong>Response:</strong> Excel file (.xlsx) with proper download headers</p>
            <p><strong>Content-Type:</strong> <code>application/vnd.openxmlformats-officedocument.spreadsheetml.sheet</code></p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api/v1';

        // Export all leads
        async function exportAllLeads() {
            const statusDiv = document.getElementById('quickStatus');
            const button = event.target;
            
            button.disabled = true;
            button.textContent = '⏳ Exporting...';
            statusDiv.innerHTML = '<div class="status info">🔄 Preparing export...</div>';

            try {
                const response = await fetch(`${API_BASE}/leads/export`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                });

                if (!response.ok) {
                    throw new Error(`Export failed: ${response.statusText}`);
                }

                // Get filename from header
                const contentDisposition = response.headers.get('Content-Disposition');
                const filename = contentDisposition
                    ? contentDisposition.split('filename=')[1].replace(/"/g, '')
                    : 'leads-export.xlsx';

                // Download file
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                statusDiv.innerHTML = `<div class="status success">✅ Successfully exported to ${filename}</div>`;

            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ Export failed: ${error.message}</div>`;
            } finally {
                button.disabled = false;
                button.textContent = '📥 Export All Leads';
            }
        }

        // Export filtered leads
        async function exportFilteredLeads() {
            const searchInput = document.getElementById('searchInput');
            const statusDiv = document.getElementById('filteredStatus');
            const button = event.target;
            const search = searchInput.value.trim();

            if (!search) {
                statusDiv.innerHTML = '<div class="status error">❌ Please enter a search term</div>';
                return;
            }

            button.disabled = true;
            button.textContent = '⏳ Exporting...';
            statusDiv.innerHTML = `<div class="status info">🔄 Exporting leads matching "${search}"...</div>`;

            try {
                const response = await fetch(`${API_BASE}/leads/export?search=${encodeURIComponent(search)}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                });

                if (!response.ok) {
                    throw new Error(`Export failed: ${response.statusText}`);
                }

                // Get filename from header
                const contentDisposition = response.headers.get('Content-Disposition');
                const filename = contentDisposition
                    ? contentDisposition.split('filename=')[1].replace(/"/g, '')
                    : 'leads-export-filtered.xlsx';

                // Download file
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                statusDiv.innerHTML = `<div class="status success">✅ Successfully exported filtered leads to ${filename}</div>`;

            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ Export failed: ${error.message}</div>`;
            } finally {
                button.disabled = false;
                button.textContent = '📥 Export Filtered Leads';
            }
        }

        // Add Enter key support for search input
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                exportFilteredLeads();
            }
        });
    </script>
</body>
</html>
